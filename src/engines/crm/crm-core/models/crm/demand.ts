import { VModel } from '@/lib/vails';
import { CrmItem } from '../../types/model';
import { compact } from 'lodash';
import dayjs from 'dayjs';

export class CrmDemandModel extends VModel<CrmItem> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return CrmTargetModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
  createdAt = this.computedAttr('createdAt', () => {
    return dayjs(this.reactiveRecord.created_at).format('YYYY-MM-DD');
  });

  formatRegion = this.computedAttr('formatRegion', () => {
    return compact([
      this.reactiveRecord.payload?.region?.province,
      this.reactiveRecord.payload?.region?.city,
      this.reactiveRecord.payload?.region?.district,
    ]).join('/');
  });

  endOn = this.computedAttr('endOn', () => {
    if (this.reactiveRecord.payload?.endOn) {
      return dayjs(this.reactiveRecord.payload?.endOn).format('YYYY/MM/DD');
    }
    return;
  });

  source = this.computedAttr('source', () => {
    if (this.reactiveRecord.payload?.origin == '单位') {
      return this.reactiveRecord.payload?.company_name || this.reactiveRecord.payload?.origin;
    }
    return this.reactiveRecord.payload?.origin;
  });
}
