import { VModel, VObject } from '@/lib/vails';
import { CrmTarget } from '../../types/model';
import { compact } from 'lodash';

export class CrmTargetModel extends VModel<CrmTarget> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return CrmTargetModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }

  formatRegion = this.computedAttr('formatRegion', () => {
    return this.reactiveRecord.portrait?.region_type == '国外'
      ? compact([this.reactiveRecord.province, this.reactiveRecord.city]).join('/')
      : compact([
          this.reactiveRecord.province,
          this.reactiveRecord.city,
          this.reactiveRecord.district,
        ]).join('/');
  });

  defaultTargetForm = this.computedAttr('defaultTargetForm', () => {
    return this.reactiveRecord.target_define?.target_form?.setting?.confs?.[0]?.conf?.form || {};
  });

  static targetForm(record: any, name: string): VObject {
    return (
      (record.target_define?.target_form?.setting?.confs || [])?.find(
        (conf: any) => conf.name == name,
      )?.conf?.form || {}
    );
  }
}
