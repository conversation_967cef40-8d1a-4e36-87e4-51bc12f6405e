import { CrmTargetModel } from './target';

export class CrmTalentTargetModel extends CrmTargetModel {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return CrmTargetModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
  userTags = this.computedAttr('userTags', () => {
    return this.reactiveRecord.portrait?.tags?.join(',');
  });

  researchFields = this.computedAttr('researchFields', () => {
    if (typeof this.reactiveRecord.portrait?.research_field === 'object') {
      return this.reactiveRecord.portrait?.research_field?.join('/');
    }
    return this.reactiveRecord.portrait?.research_field;
  });
}
