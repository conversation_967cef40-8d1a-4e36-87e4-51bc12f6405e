import { VModel } from '@/lib/vails';
import { CrmItemDefine } from '../../../types/model';

export class CrmItemDefineModel extends VModel<CrmItemDefine> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return CrmItemModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }

  defaultItemForm = this.computedAttr('defaultItemForm', () => {
    return this.reactiveRecord.item_form?.setting?.confs?.[0]?.conf?.form || {};
  });
}
