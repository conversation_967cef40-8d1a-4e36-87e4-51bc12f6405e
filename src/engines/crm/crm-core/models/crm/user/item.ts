import { VModel } from '@/lib/vails';
import { CrmItem } from '../../../types/model';
import dayjs from 'dayjs';
import { InstanceModel } from '@/engines/bpm/bpm-core/apis/user/instance.api';
import { compact } from 'lodash-es';

export class CrmItemModel extends VModel<CrmItem> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return CrmItemModel.stateMapping()[this.reactiveRecord.state];
  // });

  createdAt = this.computedAttr('createdAt', () => {
    return dayjs(this.reactiveRecord.created_at).format('YYYY-MM-DD HH:mm');
  });

  createdDay = this.computedAttr('createdDay', () => {
    return dayjs(this.reactiveRecord.created_at).format('DD');
  });

  createdYM = this.computedAttr('createdYM', () => {
    return dayjs(this.reactiveRecord.created_at).format('YYYY-MM');
  });

  nextFollowUpAt = this.computedAttr('nextFollowUpAt', () => {
    if (this.reactiveRecord.payload?.next_follow_up_at) {
      return dayjs(this.reactiveRecord.payload.next_follow_up_at).format(
        'YYYY-MM-DD HH:mm'
      );
    }
    return '';
  });

  visitAt = this.computedAttr('visitAt', () => {
    if (this.reactiveRecord.payload?.visit_at) {
      return dayjs(this.reactiveRecord.payload.visit_at).format(
        'YYYY-MM-DD HH:mm'
      );
    }
    return '';
  });

  instanceConfig = this.computedAttr('instanceConfig', () => {
    return InstanceModel.stateMap()[
      this.reactiveRecord.flowable_instance_infos?.[0]?.state
    ];
  });

  demandArea = this.computedAttr('demandArea', () => {
    if (this.reactiveRecord.payload?.region) {
      return compact([
        this.reactiveRecord.payload?.region?.province,
        this.reactiveRecord.payload?.region?.city,
        this.reactiveRecord.payload?.region?.district,
      ]).join(',');
    }
    return '';
  });

  creatorName = this.computedAttr('creatorName', () => {
    if (this.reactiveRecord.creator_type == 'App') {
      return '系统';
    }
    return this.reactiveRecord.creator?.name;
  });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }

  defaultItemForm = this.computedAttr('defaultItemForm', () => {
    return (
      this.reactiveRecord.item_define?.item_form?.setting?.confs?.[0]?.conf
        ?.form || {}
    );
  });
}
