import { VModel, VObject } from '@/lib/vails';
import { CrmClue } from '../../../types/model';
import dayjs from 'dayjs';

export class CrmClueModel extends VModel<CrmClue> {
  stateConfig = this.computedAttr('stateConfig', () => {
    return CrmClueModel.stateMapping()[this.reactiveRecord.state];
  });

  timeRange = this.computedAttr('timeRange', () => {
    return `${this.startAt.value} - ${this.endAt.value}`;
  });

  startAt = this.computedAttr('startAt', () => {
    return dayjs(this.reactiveRecord.start_at).format('YYYY/MM/DD');
  });

  endAt = this.computedAttr('endAt', () => {
    return dayjs(this.reactiveRecord.end_at).format('YYYY/MM/DD');
  });

  finishedAt = this.computedAttr('finishedAt', () => {
    if (this.reactiveRecord.finished_at) {
      return dayjs(this.reactiveRecord.finished_at).format('YYYY/MM/DD');
    }
    return '-';
  });

  createdAt = this.computedAttr('createdAt', () => {
    return dayjs(this.reactiveRecord.created_at).format('YYYY/MM/DD');
  });

  kindText = this.computedAttr('kindText', () => {
    return CrmClueModel.kindMapping()[this.reactiveRecord.kind];
  });

  timeClass = this.computedAttr('timeClass', () => {
    if (!this.reactiveRecord.end_at) return 'text-gray-400';

    if (
      this.reactiveRecord.state === 'pending' &&
      dayjs().isBefore(this.reactiveRecord.end_at) &&
      dayjs().diff(dayjs(this.reactiveRecord.end_at), 'day') <= 5
    ) {
      return 'text-yellow-400';
    } else if (
      this.reactiveRecord.state === 'pending' &&
      dayjs().isAfter(this.reactiveRecord.end_at)
    ) {
      return 'text-red-500';
    } else {
      return 'text-gray-400';
    }
  });

  bpmInstance = this.computedAttr('bpmInstance', () => {
    return this.reactiveRecord.flowable_instance_infos?.[0];
  });

  showBpmInstance = this.computedAttr('showBpmInstance', () => {
    return (
      this.bpmInstance.value && !['completed', 'terminated'].includes(this.bpmInstance.value.state)
    );
  });

  static kindMapping(): VObject {
    return {
      visit: '走访客户',
    };
  }

  static stateMapping(): VObject {
    return {
      pending: {
        label: '未完成',
        class: 'text-red-500',
        mpClass: ['text-gray-900', 'bg-gray-100'],
      },
      finished: {
        label: '已完成',
        class: 'text-green-500',
        mpClass: ['text-green-800', 'bg-green-100'],
      },
    };
  }
}
