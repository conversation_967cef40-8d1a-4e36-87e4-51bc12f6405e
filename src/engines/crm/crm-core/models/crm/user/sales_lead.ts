import { VModel, VObject } from '@/lib/vails';
import { CrmSalesLead } from '../../../types/model';
import dayjs from 'dayjs';

export class CrmSalesLeadModel extends VModel<CrmSalesLead> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return CrmSalesLeadModel.stateMapping()[this.reactiveRecord.state];
  // });

  createdAt = this.computedAttr('createdAt', () => {
    return dayjs(this.reactiveRecord.created_at).format('YYYY-MM-DD');
  });

  planEndAt = this.computedAttr('planEndAt', () => {
    if (this.reactiveRecord.plan_end_at) {
      return dayjs(this.reactiveRecord.plan_end_at).format('YYYY-MM-DD');
    }
    return '';
  });

  budgetStr = this.computedAttr('budgetStr', () => {
    return this.reactiveRecord.budget?.toLocaleString('en-US');
  });

  permissionText = this.computedAttr('permissionText', () => {
    return CrmSalesLeadModel.permissionMapping()[this.reactiveRecord.permission];
  });

  stageText = this.computedAttr('stageText', () => {
    return CrmSalesLeadModel.stageMapping()[this.reactiveRecord.stage];
  });

  teamMemberNames = this.computedAttr('teamMemberNames', () => {
    return this.reactiveRecord.team_member_names?.join(", ");
  });

  static stageMapping(): VObject {
    return {
      publish: '需求发布',
      abutment: '对接中',
      plan_offer: '方案报价',
      sign: '签约',
      success: '成功',
      failed: '失败',
    }
  }

  static permissionMapping(): VObject {
    return {
      public: '授权公开',
      private: '不公开',
    };
  }
}
