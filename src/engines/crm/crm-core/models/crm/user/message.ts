import { VModel, VObject } from '@/lib/vails';
import { CrmMessage } from '../../../types/model';
import dayjs from 'dayjs';

export class CrmMessageModel extends VModel<CrmMessage> {
  kindText = this.computedAttr('kindText', () => {
    return CrmMessageModel.kindMapping()[this.reactiveRecord.kind];
  });

  createdAt = this.computedAttr('createdAt', () => {
    return dayjs(this.reactiveRecord.created_at).format('YYYY-MM-DD HH:mm');
  });

  createdDate = this.computedAttr('createdDate', () => {
    return dayjs(this.reactiveRecord.created_at).format('MM/DD HH:mm');
  });

  title = this.computedAttr('title', () => {
    let _title;
    if (
      this.reactiveRecord.targetable_type === 'Crm::SalesLead' &&
      this.reactiveRecord.targetable
    ) {
      _title = `${this.reactiveRecord.creator?.name}发布了`;
      if (this.reactiveRecord.targetable?.budget) {
        _title += ` <span class='primary-color'> ${this.reactiveRecord.target?.name} 预算为${this.reactiveRecord.targetable?.budget}</span> 的`;
      } else {
        _title += ` <span class='primary-color'> ${this.reactiveRecord.target?.name}</span> 的`;
      }
      if (this.reactiveRecord.targetable?.payload?.other_kind) {
        _title += ` <span class='primary-color'> ${this.reactiveRecord.targetable?.payload?.other_kind}</span>`;
      } else {
        _title += ` <span class='primary-color'> ${this.reactiveRecord.targetable?.kind}</span>`;
      }
    }

    _title ||= this.reactiveRecord.payload?.title || this.reactiveRecord.content;

    return _title;
  });

  static kindMapping(): VObject {
    return {
      broadcast: '全站广播',
      dynamic_match: '动态匹配',
    };
  }
}
