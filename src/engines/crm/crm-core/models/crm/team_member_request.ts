import { VModel, VObject } from '@/lib/vails';
import { CrmTeamMemberRequest } from '../../types/model';
import dayjs from 'dayjs';

export class CrmTeamMemberRequestModel extends VModel<CrmTeamMemberRequest> {
  dutyNames = this.computedAttr('dutyNames', () => {
    return this.reactiveRecord?.creator.duty_names?.join(', ');
  });

  createdAt = this.computedAttr('createdAt', () => {
    if (this.reactiveRecord.created_at) {
      return dayjs(this.reactiveRecord.created_at).format('YYYY-MM-DD');
    }
    return '不限';
  });
  stateZH = this.computedAttr('stateZH', () => {
    return CrmTeamMemberRequestModel.statusMapping()[this.reactiveRecord.state_zh];
  });

  static statusMapping(): VObject {
    return {
      待处理: { label: '待处理', color: 'rgba(35, 56, 118, 0.5)' },
      已通过: { label: '已通过', color: 'rgba(14, 159, 110, 1)' },
      已拒绝: { label: '已拒绝', color: 'rgba(240, 82, 82, 1)' },
    };
  }
}
