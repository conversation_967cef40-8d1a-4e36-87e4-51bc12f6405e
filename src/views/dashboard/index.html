<div class="flex flex-col lg:flex-row lg:gap-4 p-6 md:p-8">
  <!-- Main Content Area -->
  <div class="flex-1 space-y-4">
      <!-- 1. 个人数据总览 (Personal Data Overview) -->
      <div class="gradient-card rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300">
          <div class="mb-6">
              <h3 class="text-lg font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">个人数据总览</h3>
          </div>
          
          <!-- 四个统计卡片 -->
          <div class="grid grid-cols-4 gap-3">
              <div class="bg-indigo-50/50 rounded-xl p-4">
                  <div class="text-sm text-blue-900">项目总数</div>
                  <div class="text-2xl font-bold text-blue-900 din-font">15</div>
              </div>
              
              <div class="bg-indigo-50/50 rounded-xl p-4">
                  <div class="text-sm text-blue-900">进行中项目</div>
                  <div class="text-2xl font-bold text-blue-900 din-font">8</div>
              </div>
              
              <div class="bg-indigo-50/50 rounded-xl p-4">
                  <div class="text-sm text-blue-900">已完成项目</div>
                  <div class="text-2xl font-bold text-blue-900 din-font">5</div>
              </div>
              
              <div class="bg-indigo-50/50 rounded-xl p-4">
                  <div class="text-sm text-blue-900">可申报活动</div>
                  <div class="text-2xl font-bold text-blue-900 din-font">12</div>
              </div>
          </div>
      </div>


      <!-- 5. 成果新增（快捷入口）(Quick Entry Icons) -->
      <div class="gradient-card rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300">
          <div class="mb-4">
              <h3 class="text-lg font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">成果新增（快捷入口）</h3>
          </div>
          <div class="quick-entry-grid">
              <div class="quick-entry-btn group">
                  <svg class="w-8 h-8 text-blue-400 mb-3 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  <span class="text-xs font-semibold text-gray-700">学术论文申报</span>
              </div>
              <div class="quick-entry-btn group">
                  <svg class="w-8 h-8 text-blue-400 mb-3 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                  </svg>
                  <span class="text-xs font-semibold text-gray-700">学术著作申报</span>
              </div>
              <div class="quick-entry-btn group">
                  <svg class="w-8 h-8 text-blue-400 mb-3 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                  </svg>
                  <span class="text-xs font-semibold text-gray-700">科研成果奖项申报</span>
              </div>
              <div class="quick-entry-btn group">
                  <svg class="w-8 h-8 text-blue-400 mb-3 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 712-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 21a4 4 0 004-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4M7 21h10"></path>
                  </svg>
                  <span class="text-xs font-semibold text-gray-700">艺术作品申报</span>
              </div>
              <div class="quick-entry-btn group">
                  <svg class="w-8 h-8 text-blue-400 mb-3 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                  </svg>
                  <span class="text-xs font-semibold text-gray-700">校刊论文投稿申请</span>
              </div>
              <div class="quick-entry-btn group">
                  <svg class="w-8 h-8 text-blue-400 mb-3 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                  </svg>
                  <span class="text-xs font-semibold text-gray-700">科研平台工作量立项</span>
              </div>
              <div class="quick-entry-btn group">
                  <svg class="w-8 h-8 text-blue-400 mb-3 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                  </svg>
                  <span class="text-xs font-semibold text-gray-700">科研横向项目立项申报</span>
              </div>
              <div class="quick-entry-btn group">
                  <svg class="w-8 h-8 text-blue-400 mb-3 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                  </svg>
                  <span class="text-xs font-semibold text-gray-700">纵向科研项目立项申报</span>
              </div>
              <div class="quick-entry-btn group">
                  <svg class="w-8 h-8 text-blue-400 mb-3 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                  </svg>
                  <span class="text-xs font-semibold text-gray-700">校级科研项目立项申报</span>
              </div>
              <div class="quick-entry-btn group">
                  <svg class="w-8 h-8 text-blue-400 mb-3 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                  </svg>
                  <span class="text-xs font-semibold text-gray-700">知识产权申报</span>
              </div>
              <div class="quick-entry-btn group">
                  <svg class="w-8 h-8 text-blue-400 mb-3 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  <span class="text-xs font-semibold text-gray-700">教师起草参编标准申报</span>
              </div>
              <div class="quick-entry-btn group">
                  <svg class="w-8 h-8 text-blue-400 mb-3 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4M8 7h8M8 7v2M16 7v2m0 0v8a2 2 0 01-2 2H10a2 2 0 01-2-2v-8m0 0h8"></path>
                  </svg>
                  <span class="text-xs font-semibold text-gray-700">科研学术活动</span>
              </div>
          </div>
      </div>


      <!-- 7. 可申报科研活动 (Available Research Activities) -->
      <div class="gradient-card rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300">
          <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">可申报科研活动</h3>
              <button class="text-sm text-blue-400 hover:text-blue-600 font-semibold hover:underline transition-all duration-300">查看全部</button>
          </div>
          <div class="space-y-4">
              <div class="border border-white/20 rounded-2xl p-4 hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-indigo-50/50 transition-all duration-300 hover:shadow-lg hover:scale-[1.02]">
                  <div class="flex items-start justify-between">
                      <div class="flex-1">
                          <div class="mb-2">
                              <div class="flex items-center flex-wrap gap-2 mb-2">
                                  <h4 class="font-semibold text-blue-900">国家自然科学基金申报</h4>
                                  <span class="px-3 py-1 text-xs bg-slate-100 text-slate-700 rounded-full">基础研究</span>
                                  <span class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-full">工程技术</span>
                              </div>
                          </div>
                          <p class="text-sm text-blue-900 mb-3">2025年度国家自然科学基金项目申报，支持基础研究和前沿探索</p>
                          <div class="flex items-center space-x-6 text-xs text-gray-500">
                              <span>申报开始: 2024-12-01</span>
                              <span>申报截止: 2025-03-20</span>
                              <span>资助额度: 20-300万</span>
                          </div>
                      </div>
                      <div class="ml-4">
                          <button class="px-6 py-3 bg-blue-500 text-white text-sm font-semibold rounded-xl hover:bg-blue-600 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">立即报名</button>
                      </div>
                  </div>
              </div>

              <div class="border border-white/20 rounded-2xl p-4 hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-indigo-50/50 transition-all duration-300 hover:shadow-lg hover:scale-[1.02]">
                  <div class="flex items-start justify-between">
                      <div class="flex-1">
                          <div class="mb-2">
                              <div class="flex items-center flex-wrap gap-2 mb-2">
                                  <h4 class="font-semibold text-blue-900">省部级科研项目申报</h4>
                                  <span class="px-3 py-1 text-xs bg-slate-100 text-slate-700 rounded-full shadow-sm">技术攻关</span>
                                  <span class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-full shadow-sm">计算机科学</span>
                              </div>
                          </div>
                          <p class="text-sm text-blue-900 mb-3">省科技厅重点研发计划项目，聚焦产业关键技术攻关</p>
                          <div class="flex items-center space-x-6 text-xs text-gray-500">
                              <span>申报开始: 2024-08-01</span>
                              <span>申报截止: 2024-10-15</span>
                              <span>资助额度: 50-200万</span>
                          </div>
                      </div>
                      <div class="ml-4">
                          <button class="px-6 py-3 border-2 border-blue-500 text-blue-400 text-sm font-semibold rounded-xl hover:bg-indigo-50/50 hover:border-blue-600 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">查看详情</button>
                      </div>
                  </div>
              </div>

              <div class="border border-white/20 rounded-2xl p-4 hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-indigo-50/50 transition-all duration-300 hover:shadow-lg hover:scale-[1.02]">
                  <div class="flex items-start justify-between">
                      <div class="flex-1">
                          <div class="mb-2">
                              <div class="flex items-center flex-wrap gap-2 mb-2">
                                  <h4 class="font-semibold text-blue-900">横向合作项目征集</h4>
                                  <span class="px-3 py-1 text-xs bg-emerald-100 text-emerald-700 rounded-full shadow-sm">产学研合作</span>
                                  <span class="px-3 py-1 text-xs bg-slate-100 text-slate-700 rounded-full shadow-sm">工程技术</span>
                              </div>
                          </div>
                          <p class="text-sm text-blue-900 mb-3">企业委托技术开发和咨询服务项目，产学研深度合作</p>
                          <div class="flex items-center space-x-6 text-xs text-gray-500">
                              <span>长期有效</span>
                              <span>合作周期: 灵活安排</span>
                              <span>资助额度: 协商确定</span>
                          </div>
                      </div>
                      <div class="ml-4">
                          <button class="px-6 py-3 bg-blue-500 text-white text-sm font-semibold rounded-xl hover:bg-blue-600 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">立即报名</button>
                      </div>
                  </div>
              </div>

              <div class="border border-white/20 rounded-2xl p-4 hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-indigo-50/50 transition-all duration-300 hover:shadow-lg hover:scale-[1.02]">
                  <div class="flex items-start justify-between">
                      <div class="flex-1">
                          <div class="mb-2">
                              <div class="flex items-center flex-wrap gap-2 mb-2">
                                  <h4 class="font-semibold text-blue-900">青年教师科研启动基金</h4>
                                  <span class="px-3 py-1 text-xs bg-slate-100 text-slate-700 rounded-full shadow-sm">青年基金</span>
                                  <span class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-full shadow-sm">人工智能</span>
                              </div>
                          </div>
                          <p class="text-sm text-blue-900 mb-3">支持新入职青年教师开展科研工作，培养科研能力</p>
                          <div class="flex items-center space-x-6 text-xs text-gray-500">
                              <span>申报开始: 2024-09-01</span>
                              <span>申报截止: 2024-11-30</span>
                              <span>资助额度: 5-15万</span>
                          </div>
                      </div>
                      <div class="ml-4">
                          <button class="px-6 py-3 border-2 border-blue-500 text-blue-400 text-sm font-semibold rounded-xl hover:bg-indigo-50/50 hover:border-blue-600 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">查看详情</button>
                      </div>
                  </div>
              </div>
          </div>
      </div>

      <!-- 2. 我的科研项目 (My Research Projects) -->
      <div class="gradient-card rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300">
          <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">我的科研项目</h3>
              <button class="text-sm text-blue-400 hover:text-blue-600 font-semibold hover:underline transition-all duration-300">查看全部</button>
          </div>
          <div class="space-y-4">
              <div class="border border-white/20 rounded-xl p-4 hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-indigo-50/50 transition-all duration-300 hover:shadow-lg">
                  <div class="flex items-start justify-between">
                      <div class="flex-1">
                          <div class="flex items-center space-x-3 mb-2">
                              <h4 class="font-semibold text-blue-900">人工智能在教育中的应用研究</h4>
                              <span class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-full">进行中</span>
                          </div>
                          <p class="text-sm text-blue-900 mb-3">探索AI技术在个性化学习、智能评估和教学辅助方面的创新应用</p>
                          <div class="flex items-center space-x-4 text-xs text-gray-500">
                              <span>开始时间: 2024-01-15</span>
                              <span>预计完成: 2024-12-31</span>
                              <span>负责人: 张研究员</span>
                          </div>
                      </div>
                      <div class="ml-4">
                          <div class="text-sm font-medium text-blue-400 mb-1">65%</div>
                          <div class="w-16 h-2 bg-gray-200 rounded-full">
                              <div class="w-2/3 h-2 bg-blue-500 rounded-full"></div>
                          </div>
                      </div>
                  </div>
              </div>
              
              <div class="border border-white/20 rounded-xl p-4 hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-indigo-50/50 transition-all duration-300 hover:shadow-lg">
                  <div class="flex items-start justify-between">
                      <div class="flex-1">
                          <div class="flex items-center space-x-3 mb-2">
                              <h4 class="font-semibold text-blue-900">大数据分析平台构建</h4>
                              <span class="px-3 py-1 text-xs bg-emerald-600 text-white rounded-full shadow-sm">已完成</span>
                          </div>
                          <p class="text-sm text-blue-900 mb-3">构建面向科研数据的分布式分析平台，支持多源异构数据处理</p>
                          <div class="flex items-center space-x-4 text-xs text-gray-500">
                              <span>开始时间: 2023-06-01</span>
                              <span>完成时间: 2024-03-15</span>
                              <span>负责人: 李博士</span>
                          </div>
                      </div>
                      <div class="ml-4">
                          <div class="text-sm font-semibold text-green-600 mb-1">100%</div>
                          <div class="w-16 h-3 bg-gray-200 rounded-full overflow-hidden">
                              <div class="w-full h-full bg-emerald-600 rounded-full shadow-sm"></div>
                          </div>
                      </div>
                  </div>
              </div>
              
              <div class="border border-white/20 rounded-xl p-4 hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-indigo-50/50 transition-all duration-300 hover:shadow-lg">
                  <div class="flex items-start justify-between">
                      <div class="flex-1">
                          <div class="flex items-center space-x-3 mb-2">
                              <h4 class="font-semibold text-blue-900">智能制造系统优化</h4>
                              <span class="px-3 py-1 text-xs bg-amber-600 text-white rounded-full shadow-sm">申报中</span>
                          </div>
                          <p class="text-sm text-blue-900 mb-3">基于机器学习的生产流程优化与质量控制技术研究</p>
                          <div class="flex items-center space-x-4 text-xs text-gray-500">
                              <span>申报时间: 2024-06-01</span>
                              <span>预计开始: 2024-09-01</span>
                              <span>负责人: 王教授</span>
                          </div>
                      </div>
                      <div class="ml-4">
                          <div class="text-sm font-semibold text-red-500 mb-1">审核中</div>
                          <div class="w-16 h-3 bg-gray-200 rounded-full overflow-hidden">
                              <div class="w-1/4 h-full bg-amber-600 rounded-full shadow-sm"></div>
                          </div>
                      </div>
                  </div>
              </div>

              <div class="border border-white/20 rounded-xl p-4 hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-indigo-50/50 transition-all duration-300 hover:shadow-lg">
                  <div class="flex items-start justify-between">
                      <div class="flex-1">
                          <div class="flex items-center space-x-3 mb-2">
                              <h4 class="font-semibold text-blue-900">区块链技术在供应链管理中的应用</h4>
                              <span class="px-3 py-1 text-xs bg-blue-600 text-white rounded-full shadow-sm">进行中</span>
                          </div>
                          <p class="text-sm text-blue-900 mb-3">研究区块链在供应链透明度和数据安全方面的技术实现</p>
                          <div class="flex items-center space-x-4 text-xs text-gray-500">
                              <span>开始时间: 2024-04-01</span>
                              <span>预计完成: 2025-03-31</span>
                              <span>负责人: 陈博士</span>
                          </div>
                      </div>
                      <div class="ml-4">
                          <div class="text-sm font-semibold text-blue-400 mb-1">35%</div>
                          <div class="w-16 h-3 bg-gray-200 rounded-full overflow-hidden">
                              <div class="w-1/3 h-full bg-blue-600 rounded-full shadow-sm"></div>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </div>

  <!-- Right Sidebar -->
  <div class="w-full lg:w-96 space-y-4">
      <!-- 3. 待办事项 (To-Do Items) -->
      <div class="gradient-card rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300">
          <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">待办事项</h3>
          </div>
          <div class="space-y-3">
              <div class="border border-gray-200 rounded-lg p-3 hover:bg-indigo-50/50 cursor-pointer" onclick="openApprovalModal('项目立项审批', '人工智能在教育中的应用研究', '2024-07-02 14:30')">
                  <div class="flex items-start justify-between">
                      <div class="flex-1">
                          <h4 class="text-sm font-medium text-gray-900">项目立项审批</h4>
                          <p class="text-sm text-blue-900 mt-1">人工智能在教育中的应用研究</p>
                          <p class="text-xs text-gray-500 mt-1">提交时间: 2024-07-02 14:30</p>
                      </div>
                      <span class="px-2 py-1 text-xs bg-blue-100 text-blue-400 rounded-full">待处理</span>
                  </div>
              </div>
              
              <div class="border border-gray-200 rounded-lg p-3 hover:bg-indigo-50/50 cursor-pointer" onclick="openApprovalModal('项目结项审批', '大数据分析平台构建', '2024-07-01 09:15')">
                  <div class="flex items-start justify-between">
                      <div class="flex-1">
                          <h4 class="text-sm font-medium text-gray-900">项目结项审批</h4>
                          <p class="text-sm text-blue-900 mt-1">大数据分析平台构建</p>
                          <p class="text-xs text-gray-500 mt-1">提交时间: 2024-07-01 09:15</p>
                      </div>
                      <span class="px-2 py-1 text-xs bg-blue-100 text-blue-400 rounded-full">待处理</span>
                  </div>
              </div>
              
              <div class="border border-gray-200 rounded-lg p-3 hover:bg-indigo-50/50 cursor-pointer" onclick="openApprovalModal('成果申报审批', '基于深度学习的图像识别算法', '2024-06-30 16:45')">
                  <div class="flex items-start justify-between">
                      <div class="flex-1">
                          <h4 class="text-sm font-medium text-gray-900">成果申报审批</h4>
                          <p class="text-sm text-blue-900 mt-1">基于深度学习的图像识别算法</p>
                          <p class="text-xs text-gray-500 mt-1">提交时间: 2024-06-30 16:45</p>
                      </div>
                      <span class="px-2 py-1 text-xs bg-blue-100 text-blue-400 rounded-full">待处理</span>
                  </div>
              </div>

          </div>
      </div>

      <!-- 4. 通知公告 (Notifications) -->
      <div class="gradient-card rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300">
          <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">通知公告</h3>
              <button class="text-sm text-blue-400 hover:text-blue-600 font-semibold hover:underline transition-all duration-300">查看全部</button>
          </div>
          <div class="space-y-3">
              <div class="flex items-center justify-between py-3 border-b border-gray-100 transition-colors cursor-pointer group">
                  <div class="flex-1 min-w-0 mr-4">
                      <h4 class="text-sm text-gray-900 group-hover:text-blue-600 truncate transition-colors">关于科研处暑假值班安排的通知</h4>
                  </div>
                  <div class="text-xs text-gray-500 whitespace-nowrap">06-30</div>
              </div>
              
              <div class="flex items-center justify-between py-3 border-b border-gray-100 transition-colors cursor-pointer group">
                  <div class="flex-1 min-w-0 mr-4">
                      <h4 class="text-sm text-gray-900 group-hover:text-blue-600 truncate transition-colors">就业中心关于第十期职业素养训练营开营通...</h4>
                  </div>
                  <div class="text-xs text-gray-500 whitespace-nowrap">06-26</div>
              </div>
              
              <div class="flex items-center justify-between py-3 border-b border-gray-100 transition-colors cursor-pointer group">
                  <div class="flex-1 min-w-0 mr-4">
                      <h4 class="text-sm text-gray-900 group-hover:text-blue-600 truncate transition-colors">关于人才培养方案建设的通知</h4>
                  </div>
                  <div class="text-xs text-gray-500 whitespace-nowrap">06-24</div>
              </div>

              <div class="flex items-center justify-between py-3 border-b border-gray-100 transition-colors cursor-pointer group">
                  <div class="flex-1 min-w-0 mr-4">
                      <h4 class="text-sm text-gray-900 group-hover:text-blue-600 truncate transition-colors">上海市哲学社会科学规划"学校思想政治教育研究"专项课题征...</h4>
                  </div>
                  <div class="text-xs text-gray-500 whitespace-nowrap">06-23</div>
              </div>
              
              <div class="flex items-center justify-between py-3 transition-colors cursor-pointer group">
                  <div class="flex-1 min-w-0 mr-4">
                      <h4 class="text-sm text-gray-900 group-hover:text-blue-600 truncate transition-colors">"上海科普教育创新奖"申报启动</h4>
                  </div>
                  <div class="text-xs text-gray-500 whitespace-nowrap">06-20</div>
              </div>
          </div>
      </div>

      <!-- 6. 项目概览图表 (Project Overview Charts) -->
      <div class="gradient-card rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 floating-animation" style="animation-delay: 1.5s;">
          <h3 class="text-lg font-bold mb-6 bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">项目概览图表</h3>
          <div class="grid grid-cols-1 gap-6">
              <div>
                  <h4 class="text-md font-semibold mb-4 text-center text-gray-700">项目状态分布</h4>
                  <div class="chart-container">
                      <canvas id="statusChart"></canvas>
                  </div>
              </div>
              <div>
                  <h4 class="text-md font-semibold mb-4 text-center text-gray-700">项目类别分布</h4>
                  <div class="chart-container">
                      <canvas id="categoryChart"></canvas>
                  </div>
              </div>
          </div>
      </div>

  </div>
</div>