<script lang='ts'>
import { ref, defineComponent, toRefs, computed, watch, PropType } from 'vue';
import ComCalcFormat from '../ComCalcFormat.vue';
import { VObject } from '../../lib/vails/model/index';
import { useContextInject } from '@/components/global/ta-component/ta-template-form-core/useContext';
const ComCaculationFormField = defineComponent({
  name: 'ComCaculationFormField',
  components: {
    ComCalcFormat,
  },
  props: {
    value: { type: [String, Number], default: 0 },
    payload: { type: Object, default: () => ({}) },
    disabled: { type: Boolean, default: false },
  },
  setup(props, { emit }) {
    const localValue = computed({
      get() {
        return props.value.toString()
      },
      set(val: any) {
        emit('update:value', val)
      }
    })
    const isShow = ref(false)
    const toShow = () => {
      isShow.value = !isShow.value
    }
    const data = ref<any[]>([])

    const localPayload = computed<VObject>({
      get() {
        return props.payload;
      },
      set(value) {
        emit('update:payload', value);
      },
    });
    const { context } = useContextInject();
    watch(() => context?.__unit_define_ids_attrs, () => {
      if (context?.__unit_define_ids_attrs) {
        data.value = context.__unit_define_ids_attrs.map((item: any) => item.name)
      } else {
        data.value = []
      }
    }, { deep: true })
    return {
      ...toRefs(props),
      toShow,
      isShow,
      localValue,
      data,
    };
  },
});
export default ComCaculationFormField;
</script>

<template lang="pug">
view.com-caculation-form-field
  .text-sm(@click='toShow') {{ localValue }}
  //- TaInput(v-model:value='value',:disabled='true',@click='toShow')
  ComCalcFormat(v-if='isShow',v-model:value='localValue',:data='data')
</template>

<style lang="stylus"></style>
