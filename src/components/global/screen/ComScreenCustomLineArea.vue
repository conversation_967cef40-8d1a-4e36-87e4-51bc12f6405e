<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';
import * as echarts from 'echarts';
const ComScreenCustomLineArea = defineComponent({
  name: 'ComScreenCustomLineArea',
  components: {},
  props: {
    lines: {
      type: Array,
      default: () => [{
        name: '学术论文',
        data: [1, 3, 5, 2, 4]
      }, {
        name: '学术著作',
        data: [4, 5, 3, 1, 2]
      }]
    },
    xData: {
      type: Array,
      default: () => [2020, 2021, 2022, 2023, 2024]
    },
  },
  setup(props) {
    const echartsRef = ref(null);

    const lineNormalize = (line: any) => {
      return {
        name: line.name,
        type: 'line',
        showSymbol: false,
        smooth: true,
        areaStyle: {
          opacity: 0.2
        },
        lineStyle: {
          width: 3,
        },
        emphasis: {
          focus: 'series'
        },
        data: line.data
      }
    }

    const lineOption = computed(() => ({
      color: ['#3F83F8', '#16BDCA'],
      grid: {
        top: '14%',
        left: '-3%',
        right: '1%',
        bottom: '1%',
        containLabel: true
      },
      legend: {
        show: true,
        icon: 'rect',
        itemGap: 16,
        itemWidth: 12,
        itemHeight: 4,
        orient: 'horizontal',
        textStyle: {
          color: '#4B5563',
        },
        width: '80%',
        right: 'right'
      },
      xAxis: {
        type: 'category',
        data: props.xData,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        show: false,
        type: 'value'
      },
      series: props.lines.map(lineNormalize),

    }))
    return {
      ...toRefs(props),
      lineOption,
      echartsRef,
    };
  },
});
export default ComScreenCustomLineArea;
</script>

<template lang="pug">
.com-screen-custom-line-area.w-full.h-full
  TaScreenEchartBase(:options='lineOption' ref='echartsRef')
</template>

<style lang="stylus" scoped></style>
