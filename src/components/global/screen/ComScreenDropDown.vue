<script lang='ts'>
import { ref, defineComponent, toRefs } from 'vue';
import { useContextInject } from '@/engines/bpm/bpm-core/ta-template-form-core/useContext';
import { useFetchScreenData, useScreenDataFetchCollectionInject } from '../ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { useScreenDesignerConfigInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDesignerConfig';
const ComScreenDropDown = defineComponent({
  name: 'ComScreenDropDown',
  components: {},
  props: {},
  setup(props) {
    const menus = ['全部', '2024', '2023', '2022', '2021', '2020']
    const defaultUrl = 'url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/eduction-web/dpdpdp.png)'
    const activeIndex = ref(0)
    const { context } = useContextInject();

    const { refreshAll } = useFetchScreenData();
    const { dataResult } = useScreenDataFetchCollectionInject(props);


    const handleMenuClick = (e: any) => {
      activeIndex.value = e.key
      context.year = e.key === 0 ? '1' : menus[e.key]
      console.log('refreshAll')
      refreshAll()
    }
    return {
      ...toRefs(props),
      handleMenuClick,
      menus,
      defaultUrl,
      activeIndex,
    };
  },
});
export default ComScreenDropDown;
</script>

<template lang="pug">
.com-screen-dropdown.flex.items-center
  .text-sm.text-white.font-normal 年份：
  a-dropdown
    template(#overlay)
      a-menu(@click="handleMenuClick")
        a-menu-item(v-for='(menu, index) in menus', :key='index') {{ menu }}
    .dropdown__wrapper.p-10px.flex.justify-between.items-center.w-100px.bg-cover
      .text-sm.text-white.font-normal {{ menus[activeIndex] }}
      TaIcon(type='DownOutlined',color='white')
</template>

<style lang="stylus" scoped>
.com-screen-dropdown
  .dropdown__wrapper
    background-image v-bind(defaultUrl)
</style>
