<script lang='ts'>
import { ref, defineComponent, toRefs } from 'vue';
import dayjs from 'dayjs';
const ComScreenClock = defineComponent({
  name: 'ComScreenClock',
  components: {},
  props: {},
  setup(props) {
    const time = ref(dayjs().format('YY.MM.DD HH:mm:ss'))
    setInterval(() => {
      time.value = dayjs().format('YY.MM.DD HH:mm:ss')
    }, 1000);
    return {
      ...toRefs(props),
      time
    };
  },
});
export default ComScreenClock;
</script>

<template lang="pug">
.com-screen-clock.w-full.text-right.pr-4
  | {{ time }}
</template>

<style lang="stylus" scoped>
.com-screen-clock
  color #7D86B3
  font-family 'Digital'
  font-size 20px
  font-style normal
  font-weight 5
  line-height normal
</style>
