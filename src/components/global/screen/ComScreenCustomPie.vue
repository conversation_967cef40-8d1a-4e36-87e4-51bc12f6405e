<script lang='ts'>
import { ref, defineComponent, toRefs, computed, watch } from 'vue';
import * as echarts from 'echarts';
const ComScreenCustomPie = defineComponent({
  name: 'ComScreenCustomPie',
  components: {},
  props: {
    data: {
      type: Array, default: () => [
        { value: 0, name: '暂无数据' }
      ]
    }
  },
  setup(props) {
    const echartsRef = ref<any>(null);
    const pieOption = computed(() => ({
      color: ['#3F83F8', '#16BDCA', '#31C48D', '#FACA15', '#FF8A4C', '#F05252', '#E74694'],
      legend: {
        orient: 'vertical',
        top: 'middle',
        right: 'right',
        icon: 'circle',
        textStyle: {
          color: '#4B5563'
        }
      },
      series: [
        {
          name: 'pie',
          type: 'pie',
          radius: '90%',
          center: ['20%', '50%'],
          itemStyle: {
            borderColor: '#111928',
            borderWidth: 1,
          },
          label: {
            show: false,
          },
          data: Array.isArray(props.data) ? props.data : [props.data],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }))


    return {
      ...toRefs(props),
      pieOption,
      echartsRef,
    };
  },
});
export default ComScreenCustomPie;
</script>

<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='pieOption' ref='echartsRef')
</template>

<style lang="stylus" scoped></style>
