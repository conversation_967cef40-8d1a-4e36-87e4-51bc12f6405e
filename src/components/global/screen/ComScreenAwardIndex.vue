<script lang='ts'>
import { ref, defineComponent, toRefs, onMounted, watch, computed } from 'vue';
import ComScreenCustomTaIndexView from './ComScreenCustomTaIndexView.vue';
import { VStore } from '@/lib/vails';
import { ResearchScreenEntriesApi } from '../../../apis/research/screen/entries.api';
import { ResearchEntryModel } from '../../../models/research/screen/entries';
import { get } from 'lodash-es';
import { useContextInject } from '@/engines/bpm/bpm-core/ta-template-form-core/useContext';
const ComScreenAwardIndex = defineComponent({
  name: 'ComScreenAwardIndex',
  components: { ComScreenCustomTaIndexView },
  props: {},
  setup(props) {
    const { context } = useContextInject();
    const params = computed(() => ({
      q: {
        type_eq: 'Research::Honor',
        state_eq: 'finish_done',
        scopes: { find_record_by_year: context.year || 1 }
      },
    }))
    const store = new VStore(new ResearchScreenEntriesApi({}), ResearchEntryModel)

    const entryType = ["国家级", "省(市)部级", "社会力量办奖", "局级", "其他"]
    const cols = [
      {
        label: '部门名称',
        prop: 'department_name',
        width: '40%',
      },
      {
        label: '获奖层级',
        prop: 'entry_type',
        width: '19%',
      },
      {
        label: '获奖类别',
        prop: 'sub_type',
        width: '22%',
        justify: 'center',
      },
      {
        label: '获奖等级',
        prop: 'level',
        width: '19%',
        justify: 'center',
      },
    ];

    onMounted(() => {
      store.index({ per_page: 9999, ...params.value })
    })


    watch(() => context.year, () => {
      console.log('year changed', params.value)
      store.index({ per_page: 9999, ...params.value })
    })
    return {
      ...toRefs(props),
      store,
      cols,
      get,
      entryType,
    };
  },
});
export default ComScreenAwardIndex;
</script>

<template lang="pug">
ComScreenCustomTaIndexView(
  :cols='cols',
  :records='store.records'
)
  template(#cell='{record,col}')
    .w-full.truncate.text-white(
      v-if='col.prop === "entry_type"'
      :style='`text-align:${col.justify||"start"}`'
    ) {{entryType[get(record, col.prop)]}}
</template>

<style lang="stylus" scoped></style>
