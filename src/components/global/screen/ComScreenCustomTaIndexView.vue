<script lang='ts'>
import { defineComponent, toRefs } from 'vue';
import { Vue3Marquee } from 'vue3-marquee';
import { get } from 'lodash-es';
const ComScreenCustomTaIndexView = defineComponent({
  name: 'ComScreenCustomTaIndexView',
  components: { Vue3Marquee },
  props: {
    records: { type: Array, default: () => [] },
    // const cols = [
    // {
    //     label: '站点名称',
    //     prop: 'target.name',
    //     width: '65%',
    //   },
    //   {
    //     label: '申请人',
    //     prop: 'user.name',
    //     width: '30%',
    //     justify: 'center',
    //   },
    // ];
    cols: {
      type: Array,
      default: () => [],
    },
  },
  setup(props) {

    return {
      ...toRefs(props),
      get,
    };
  },
});
export default ComScreenCustomTaIndexView;
</script>

<template lang="pug">
.com-screen-custom-ta-index-view.w-full.overflow-hidden
  .fix__header.w-full.flex.px-4.py-2(style='background: linear-gradient(0deg, #1C3B68 0%, rgba(47, 61, 82, 0.09) 100%);')
    .text-sm.flex.items-center.text-white(
      v-for='col in cols',
      :style='`width:${col.width||"initial"};text-align:${col?.justify||"start"}`'
    ) {{ col.label }}
  Vue3Marquee.h-full(
    vertical,
    style='width:100% !important;'
    v-if='records.length > 0'
    :duration='records.length * 2',
  )
    .scroll__col.w-full.flex.px-4.py-2(v-for='record in records')
      .text-sm.flex.items-center.overflow-hidden(
        v-for='col in cols',
        :style='`width:${col.width||"initial"};justify-content:${col.justify||"start"}`'
      )
        slot(name='cell',:col='col',:record='record')
          .w-full.truncate.text-white(:style='`text-align:${col.justify||"start"}`') {{ get(record,col.prop) }}

</template>

<style lang="stylus" scoped></style>
