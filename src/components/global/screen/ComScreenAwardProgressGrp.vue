<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';
const ComScreenAwardProgressGrp = defineComponent({
  name: 'ComScreenAwardProgressGrp',
  components: {},
  props: {
    data: {
      type: Array,
      default: () => [
        { name: '一等奖', value: 10 },
        { name: '二等奖', value: 20 },
        { name: '三等奖', value: 30 },
      ]
    }
  },
  setup(props) {

    const maxIndex = computed(() => props.data.reduce((maxIndex: number, current: any, index, array: any) => {
      return current.value > array[maxIndex].value ? index : maxIndex;
    }, 0))
    return {
      ...toRefs(props),
      maxIndex,
    };
  },
});
export default ComScreenAwardProgressGrp;
</script>

<template lang="pug">
.com-screen-award-progress-grp.w-full.h-full.flex.flex-col.justify-between
  .flex.items-center(v-for='(item,index) in data')
    .label.mr-2.text-sm.text-white {{ item.name }}
    .progress__wrapper.flex-grow.pr-12
      .progress.h-2.relative(
        :style="{ width: `${item.value / data[maxIndex].value * 100}%` }"
      )
        .num.text-sm.text-white.absolute {{ item.value }}个

</template>

<style lang="stylus" scoped>
.com-screen-award-progress-grp
  .progress
    background-color #1c64f2
    .num
      top -68%
      left calc(100% + 8px)
      width max-content
</style>
