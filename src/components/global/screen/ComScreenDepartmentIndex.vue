<script lang='ts'>
import { ref, defineComponent, toRefs, computed, watch } from 'vue';
import ComScreenCustomTaIndexView from './ComScreenCustomTaIndexView.vue';
const ComScreenDepartmentIndex = defineComponent({
  name: 'ComScreenDepartmentIndex',
  components: { ComScreenCustomTaIndexView },
  props: {
    a: { type: Object, default: () => ({}) },
    b: { type: Object, default: () => ({}) },
    c: { type: Object, default: () => ({}) },
    d: { type: Object, default: () => ({}) }
  },
  setup(props) {
    const departments = computed(() => [props.a, props.b, props.c, props.d].reduce((acc, cur) => {
      Object.keys(cur).forEach(key => {
        acc.add(key);
      })
      return acc
    }, new Set()))

    const records = computed(() => Array.from(departments.value.values()).map((d: any) => ({
      name: d,
      a: props.a[d] || 0,
      b: props.b[d] || 0,
      c: props.c[d] || 0,
      d: props.d[d] || 0,
    })))

    const cols = [
      {
        label: '部门名称',
        prop: 'name',
        width: '40%',
      },
      {
        label: '纵向',
        prop: 'a',
        width: '15%',
      },
      {
        label: '横向',
        prop: 'b',
        width: '15%',
      },
      {
        label: '科研成果',
        prop: 'c',
        width: '15%',
      },
      {
        label: '技术转移转化',
        prop: 'd',
        width: '15%',
        justify: 'center',
      },
    ];

    return {
      ...toRefs(props),
      departments,
      records,
      cols,
    };
  },
});
export default ComScreenDepartmentIndex;
</script>

<template lang="pug">
ComScreenCustomTaIndexView.com-index(
  :records='records',
  :cols='cols',
)
</template>

<style lang="stylus" scoped>
.com-index
  :deep(.scroll__col)
    @apply py-4;
  :deep(.fix__header)
    @apply py-4;
</style>
