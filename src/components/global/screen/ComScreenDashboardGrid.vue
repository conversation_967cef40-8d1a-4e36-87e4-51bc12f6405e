<script lang='ts'>
import { defineComponent, toRefs, PropType, computed } from 'vue';
import { thousandString } from '../../../engines/screen/utils/util';
type Item = {
  name: string;
  num: number;

  //单位
  unit: string;
  //增量
  delta: number;
}

const ComScreenDashboardGrid = defineComponent({
  name: 'ComScreenDashboardGrid',
  components: {},
  props: {
    data: {
      type: Array as PropType<Item[]>, default: () => [
        { name: '科研项目', num: 500, unit: '个', delta: 82 },
        { name: '科研成果', num: 8692, unit: '个', delta: 82 },
        { name: '技术转移转化', num: 762, unit: '个', delta: 7 },
        { name: '横向课题到款额', num: 7792, unit: '万元', delta: 0 }
      ]
    }
  },
  setup(props) {
    const compData = computed(() => props.data.map(item => ({ ...item, num: thousandString((item.num || 0).toString()) })))
    return {
      ...toRefs(props),
      compData,
    };
  },
});
export default ComScreenDashboardGrid;
</script>

<template lang="pug">
.com-screen-dashboard-grid.grid.grid-cols-4.gap-3
  .grid__item.rounded-lg.p-4.flex.flex-col(v-for='(item,index) in compData')
    .name.text-lg.text-white.font-medium.mb-auto {{ item.name }}
    .num.text-white.relative {{ item.num }}#[span.unit {{ item.unit }}]
      .delta.absolute.right-0.top-0.text-lg(v-if='item.delta > 0') +{{ item.delta }}
</template>

<style lang="stylus" scoped>
.com-screen-dashboard-grid
  .grid__item:nth-child(1)
    background: linear-gradient(108deg, #078DEC -26.28%, #2EBCF0 85.77%);
  .grid__item:nth-child(2)
    background: linear-gradient(247deg, #078DEC 6.85%, #2966DC 100%);
  .grid__item:nth-child(3)
    background: linear-gradient(297deg, #7B57E9 11.75%, #AB1CFE 100%);
  .grid__item:nth-child(4)
    background: linear-gradient(299deg, #078DEC -0.04%, #2966DC 100%)
  .grid__item
    .num
      width fit-content
      font-family "DIN Alternate"
      font-size 36px
      font-style normal
      font-weight 700
      line-height normal
      .unit
        padding-left 2px
        font-family "PingFang SC"
        font-size 14px
        font-weight 400
      .delta
        right -24px
</style>
