<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';
const ComScreenCustomSvgProgress = defineComponent({
  name: 'ComScreenCustomSvgProgress',
  components: {},
  props: {
    //[{ name: 'a', percent: 70 }]
    records: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    const data = computed(() => props.records)
    return {
      ...toRefs(props),
      data,
    };
  },
});
export default ComScreenCustomSvgProgress;
</script>

<template lang="pug">
ComScreenBaseSvgProgress(
  :data='data',
  :radius='85',
  :strokeWidth='13',
  :colorArr="['#F3654A']",
  textColor='white',
  fontSize='36'
  :baseStrokeWidth="8"
  baseStrokeColor='rgba(217, 225, 231, 0.1)'
)
</template>

<style lang="stylus" scoped></style>
