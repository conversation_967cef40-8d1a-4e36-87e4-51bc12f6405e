<script lang="ts">
import { ref, defineComponent, toRefs, PropType } from 'vue';
import ComCardHead from '@/components/ComCardHead.vue';
import ComSummaryHead from '@/components/ComSummaryHead.vue';
import { ReportItem } from '@/types/model';
import { VApi, VStore, VObject } from '@/lib/vails';
import { ReportItemModel } from '@/engines/report/report-core/models/report/user/execute/items';
import { message } from 'ant-design-vue';
import ComSummary from '@/components/ComSummary.vue';

const ComSummaryAlone = defineComponent({
  name: 'ComSummaryAlone',
  components: { ComCardHead, ComSummaryHead, ComSummary },
  props: {
    editable: { type: Boolean, default: false },
    head: { type: String, default: '工作总结/计划' },

    token: { type: Object, required: true },
    rule: { type: Object, required: true },
    itemApi: { type: Object as PropType<VApi<ReportItem, string>>, required: true },
  },
  setup(props) {
    const itemStore = new VStore(props.itemApi, ReportItemModel);

    const editRecord = ref<VObject>({});

    const fetchData = () => {
      itemStore.index().then(() => {
        if (itemStore.records.value.length > 0) {
          // 编辑
          editRecord.value = itemStore.records.value[0];
          if (!editRecord.value.formData.payload) {
            editRecord.value.formData.payload = {};
          }
        } else {
          // 创建
          editRecord.value = itemStore.new({
            payload: {},
          });
        }
      });
    };

    fetchData();

    return {
      ...toRefs(props),
      editRecord,
      fetchData,
    };
  },
});
export default ComSummaryAlone;
</script>

<template lang="pug">
.com-summary-alone.w-full.py-5.pr-5.pl-2
  ComSummary(
    :record='editRecord',
    :rule='rule',
    :head='head',
    :editable='editable',
    @fetch='fetchData'
  )
</template>

<style lang="stylus" scoped>
.primary-color
  color $primary-color
</style>
