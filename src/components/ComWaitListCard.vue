<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';
import dayjs from 'dayjs';
const ComWaitListCard = defineComponent({
  name: 'ComWaitListCard',
  components: {},
  props: {
    record: { type: Object, default: () => {} },
  },
  setup(props) {
    return {
      ...toRefs(props),
      dayjs,
    };
  },
});
export default ComWaitListCard;
</script>

<template lang="pug">
.com-wait-list-card.w-full
  .top-part.flex.justify-between.items-start.mb-1
    .top-left.flex.items-center
      img.w-4.h-4.mr-2(src='https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/school/%E4%B8%9A%E5%8A%A1%E6%B5%81%E7%A8%8B%201.png')
      .text-gray-400.text-xs 业务流程申请
    .time.text-gray-400.text-xs {{dayjs(record?.created_at).format('MM-DD HH:mm')}}
  .bottom-part.ml-6.mb-4
    .name.text-gray-800.text-sm.truncate {{record?.workflow_name}}

</template>

<style lang="stylus" scoped>
.com-wait-list-card
  border-bottom 1px solid #E5E7EB
  .bottom-part
    .name
      transition-duration .5s
      &:hover
        color #1890ff
</style>
