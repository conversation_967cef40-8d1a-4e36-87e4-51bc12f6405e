<script lang="ts">
import { VStore } from '@/lib/vails';
import { ref, defineComponent, toRefs, computed, onMounted, watch, reactive, toRaw } from 'vue';
import { TofuStarApi } from '@/apis/tofu/star.api';
import { TofuWebsiteApi } from '@/apis/tofu/website.api';
import ComAppsCardTabs from '@/components/ComAppsCardTabs.vue';
import { message } from 'ant-design-vue';
const ComHomeAppsCard = defineComponent({
  name: 'ComHomeAppsCard',
  components: { ComAppsCardTabs },
  props: {
    store: { type: Object, required: true },
  },
  setup(props) {
    const starStore = new VStore(new TofuStarApi());
    const webStore = new VStore(new TofuWebsiteApi());
    const tabs = computed(() => [
      {
        key: 'app',
        label: '应用系统',
        query: {},
        store: props.store,
      },
      {
        key: 'star',
        label: '收藏应用',
        query: {},
        store: starStore,
      },
      {
        key: 'website',
        label: '网站入口',
        query: {},
        store: webStore,
      },
    ]);
    const activeTab = ref(tabs.value[0]);
    const pagination = reactive({ page: 1, current: 1 });
    const width = ref(6);
    const staredArr = ref<any>([]);
    onMounted(() => {
      props.store
        .index()
        .then(() => (pagination.page = Math.ceil(props.store?.records.value.length / 12))); //这个接口没有分页
      starStore
        .index()
        .then(() => starStore.records.value.map(star => staredArr.value.push(star.id)));
      webStore.index();
      window.addEventListener('resize', () => {
        return (width.value = document.body.clientWidth < 1180 ? 5 : 6);
      });
    });

    const toLink = (url: string) => {
      if ([...url][0] !== 'h') {
        window.open(`http://os.stiei.edu.cn${url}`);
      } else {
        window.open(`${url}`);
      }
    };
    const fetchData = (emitData: any, keyword: any, keywordArray: any) => {
      switch (activeTab.value.key) {
        case 'app':
          props.store.index({ q: { name_cont_any: keyword } });
          break;
        case 'star':
          starStore.index({ q: { name_cont_any: keyword } });
          break;
        case 'website':
          webStore.index({ q: { name_cont_any: keyword } });
          break;
      }
      // props.store.index({ q: { name_eq: keyword } });
    };
    const pageSlice = (arr: any[]) => {
      return arr.slice((pagination.current - 1) * 12, pagination.current * 12);
    };
    const changeCurrentPage = (index: number) => {
      pagination.current = index + 1;
    };
    const changeTab = (tab: any) => {
      activeTab.value = tab;
      tab.store.index();
      pagination.current = 1;

      pagination.page = Math.ceil(tab.store?.records.value.length / 12);
    };
    const toStar = async (id: number) => {
      if (isStared(id)) {
        await new TofuStarApi()
          .batchUpdate(staredArr.value.filter((star: number) => star !== id))
          .then(() => (staredArr.value = staredArr.value.filter((star: number) => star !== id)))
          .catch(e => message.error('取消收藏失败'));
        props.store.index();
        message.success('取消收藏成功');
      } else {
        await new TofuStarApi()
          .batchUpdate([id, ...staredArr.value])
          .then(() => (staredArr.value = [id, ...staredArr.value]))
          .catch(e => message.error('收藏失败'));
        props.store.index();
        message.success('收藏成功');
      }

      // fetchData(null, null, null);
    };
    const isStared = (id: number) => {
      return staredArr.value.indexOf(id) !== -1 ? true : false;
    };
    return {
      ...toRefs(props),
      tabs,
      toLink,
      fetchData,
      starStore,
      webStore,
      activeTab,
      pagination,
      changeTab,
      pageSlice,
      changeCurrentPage,
      toStar,
      staredArr,
      isStared,
    };
  },
});
export default ComHomeAppsCard;
</script>

<template lang="pug">
.com-home-apps-card.w-full.bg-white
  .apps__card__header.flex.justify-between.px-4.items-center
    ComAppsCardTabs.h-14.cursor-pointer(:tabs='tabs',@change='changeTab')
    TaSearcher.mr-4.searcher(:isShowButton='true',@update:value='fetchData')
  .apps__card__content.p-6.h-276px.flex.flex-col.justify-between
    .grid.grid-cols-6.mb-2.gap-4
      .app.flex.flex-col.items-center.cursor-pointer.relative(v-for='app in pageSlice(store.records.value)',@click='toLink(app.url)',v-if='activeTab.key ==="app" ')
        img.w-50px.h-50px.mb-2(:src='`${app.image||"https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/stiei-official-website/%23icon.png"}`')
        .app_name.text-sm.w-56px.text-gray-600.h-10.overflow-hidden.text-center {{app.name}}
        TaIcon.absolute.text-gray-400.right-0.top-0.z-2(type='solid/star',style='width:1rem;height:1rem',@click.stop='toStar(app.id)',:class='`${isStared(app.id)?"!text-blue-500":""}`')

      .app.flex.flex-col.items-center.cursor-pointer(v-for='app in pageSlice(starStore.records.value)',@click='toLink(app.url)',v-else-if = 'activeTab.key === "star"')
        img.w-50px.h-50px.mb-2(:src='`${app.image||"https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/stiei-official-website/%23icon.png"}`')
        .app_name.text-sm.w-56px.text-gray-600.h-10.overflow-hidden.text-center {{app.name}}
      .app.flex.flex-col.items-center.cursor-pointer(v-for='app in pageSlice(webStore.records.value)',@click='toLink(app.url)',v-else-if='activeTab.key === "website"')
        img.w-50px.h-50px.mb-2(:src='`${app.image||"https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/stiei-official-website/%23icon.png"}`')
        .app_name.text-sm.w-56px.text-gray-600.h-10.overflow-hidden.text-center {{app.name}}
    .pagination.flex.w-full.items-center.justify-center.gap-3
      .dot.w-2.h-2.rounded-full.cursor-pointer(v-for='(dot,index) in pagination.page',:class='`${pagination.current === index +1?"bg-blue-500":"bg-gray-200"}`',@click='changeCurrentPage(index)')


</template>

<style lang="stylus">
.com-home-apps-card
  box-shadow 0px 1px 3px rgba(0, 0, 0, 0.1), 0px 1px 2px -1px rgba(0, 0, 0, 0.1)
  border-radius 8px
  .apps__card__header
    border-bottom: 1px solid #e5e7eb
    @media(max-width:1180px) {
      @apply flex-col
    }
  .apps__card__content
    .app
      transition all .3s
      &:hover
        transform translateY(-4px)
    .app_name
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      text-overflow: ellipsis;
    .pagination
      .dot
        transition all .3s

.searcher
  border 1px solid #E5E7EB
  width 280px !important
  @media(max-width:1180px) {
    margin-left 16px
    height 37px !important
    width 432px !important
  }
</style>
