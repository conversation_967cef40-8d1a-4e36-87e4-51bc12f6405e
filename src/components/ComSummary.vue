<script lang="ts">
import { ref, defineComponent, toRefs, PropType } from 'vue';
import ComCardHead from '@/components/ComCardHead.vue';
import ComZongjieHead from '@/components/ComSummaryHead.vue';
import { ReportItem } from '@/types/model';
import { VApi, VStore, VObject } from '@/lib/vails';
import { ReportItemModel } from '@/engines/report/report-core/models/report/user/execute/items';
import { message } from 'ant-design-vue';

const ComSummary = defineComponent({
  name: 'ComSummary',
  components: { ComCardHead, ComZongjieHead },
  props: {
    editable: { type: Boolean, default: false },
    head: { type: String, default: '工作总结/计划' },

    rule: { type: Object, required: true },
    record: { type: Object, required: true },
  },
  setup(props, { emit }) {
    const visibleForm = ref(false);

    const onEdit = () => {
      visibleForm.value = true;
    };

    const onFormConfirm = () => {
      props.record
        .save()
        .then(() => {
          message.success('编辑成功');
          emit('fetch');
        })
        .catch(() => {
          message.error('编辑失败');
        });
    };

    return {
      ...toRefs(props),
      visibleForm,
      onEdit,
      onFormConfirm,
    };
  },
});
export default ComSummary;
</script>

<template lang="pug">
.com-summary.w-full.py-5.px-5
  ComCardHead.mb-5(:title='head')
    TaSvg.h-5.w-5.primary-color.cursor-pointer(
      v-if='rule.form?.rule_form && editable',
      src='https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/tailwind-icons/outline/pencil.svg'
      @click='onEdit'
    )
  //- ComSummary.bg-white(:record="editRecord")
  div.flex.w-full.space-x-2
    div.mb-5.flex-glow.space-y-2(class='w-[69%]')
      ComZongjieHead.mb-5
      div.line 一.市场拓展
        div.twelve.pt-1.break-all(v-for='(item,index) in record.payload?.list') <b>{{index+1}}.</b> {{ item?.市场拓展 }}
      div.line 二.投标工作
        div.twelve.pt-1.break-all(v-for='(item,index) in record.payload?.list_2') <b>{{index+1}}.</b> {{item?.投标工作}}
      div.line 三.已落地项目管理
        div.twelve.pt-1.break-all(v-for='(item,index) in record.payload?.list_3') <b>{{index+1}}.</b> {{item?.已落地项目管理}}
      div.line 四.其他工作
        div.twelve.pt-1.break-all(v-for='(item,index) in record.payload?.list_4') <b>{{index+1}}.</b> {{item?.其他工作}}
    div.mb-5.flex-glow.space-y-2(class='w-[28%]')
      ComZongjieHead.mb-5(:title='"下周工作计划"')
      div.line 一.市场拓展
        div.twelve.pt-1.break-all(v-for='(item,index) in record.payload?.list_next') <b>{{index+1}}.</b> {{item?.下周市场拓展计划}}
      div.line 二.投标工作
        div.twelve.pt-1.break-all(v-for='(item,index) in record.payload?.list_next_2') <b>{{index+1}}.</b> {{item?.下周投标工作}}
      div.line 三.已落地项目管理
        div.twelve.pt-1.break-all(v-for='(item,index) in record.payload?.list_next_3') <b>{{index+1}}.</b> {{item?.下周落地项目管理计划}}
      div.line 四.其他工作
        div.twelve.pt-1.break-all(v-for='(item,index) in record.payload?.list_next_4') <b>{{index+1}}.</b> {{item?.下周其他工作}}
  TaTemplateFormModal(
    v-if='rule.form?.rule_form && record.formData'
    v-model:visible='visibleForm',
    :template='rule.form.rule_form',
    v-model:modelValue='record.formData.payload',
    @confirm='onFormConfirm'
  )
</template>

<style lang="stylus" scoped>
.primary-color
  color $primary-color
.line
  font-size: 18px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 600;
  color: #737373;
  line-height: 30px;
.twelve
  font-size: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 30px;
</style>
