<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';
const ComFourTongJi = defineComponent({
  name: 'ComFourTongJi',
  components: {},
  props: {
    value: { type: String, default: 'copy' },
    title: { type: String, default: '周报统计' },
    store: { type: Object, required: true },
    // statistics: { type: Object, required: true },
  },
  setup(props) {
    let msg = ref('');
    const plus = (a: any, b: any) => {
      if (typeof a === 'undefined') {
        return 0 + b;
      } else if (typeof b === 'undefined') {
        return a + 0;
      } else {
        return a + b;
      }
    };
    const data = computed(() => [
      {
        title: '周报总数',
        num: props.store.cache.value['statistics']?.count,
        url: '',
        color: '#DBEAFE',
        type: 'ContainerOutlined',
      },
      // {
      //   title: '应交人数',
      //   num: 30,
      //   url: '',
      //   color: '#E0E7FF',
      //   type: 'TeamOutlined',
      // },
      {
        title: '已提交',
        num:
          plus(
            props.store.cache.value['statistics']?.complete,
            props.store.cache.value['statistics']?.delay_complete,
          ) || 0,
        url: '',
        color: '#D1FAE5 ',
        type: 'CheckCircleOutlined',
      },
      // {
      //   title: '逾期提交',
      //   num: props.store.cache.value['statistics']?.delay_complete || 0,
      //   url: '',
      //   color: '#FEF3C7',
      //   type: 'ClockCircleOutlined',
      // },
      {
        title: '未提交',
        num: props.store.cache.value['statistics']?.todo || 0,
        url: '',
        color: '#FEE2E2',
        type: 'CloseOutlined',
      },
    ]);
    console.log(props.store.cache['statistics']);
    return {
      ...toRefs(props),
      msg,
      data,
    };
  },
});
export default ComFourTongJi;
</script>

<template lang="pug">
.com-four-tong-ji.w-full.pt-5.pl-5
  div.head.flex.w-full.justify-between.mb-5
    div.flex
      div.shape.mr-3
      div.title {{title}}
    div
  div.flex.w-full
    div.flex-grow(v-for='(item,index) in data')
      div.flex.justify-center.items-center(class="border-r border-r-[#E4E4E7]",v-if='index!=3')
        div.circle.flex.items-center.justify-center(class="mr-[22px]",:style="`background:${item.color};`")
          TaIcon(:type="`${item.type}`",:size='18')
        div
          div.subTitle {{item.title}}
          div.num {{item.num}}
      div.flex.justify-center.items-center(v-if='index==3')
        div.circle.flex.items-center.justify-center(class="mr-[22px]",:style="`background:${item.color};`")
          TaIcon(:type="`${item.type}`",:size='18')
        div
          div.subTitle {{item.title}}
          div.num {{item.num}}

</template>

<style lang="stylus" scoped>
.shape
  width: 8px;
  height: 24px;
  background: #BFDBFE;
  border-radius: 7px;
.title
  font-size: 16px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #18181B;
  line-height: 24px;
.circle
  width: 48px;
  height: 48px;
  background: #DBEAFE;
  border-radius: 24px;
.subTitle
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #94A3B8;
  line-height: 18px;
.num
  font-size: 36px;
  font-family: Inter-Bold, Inter;
  font-weight: bold;
  color: #334155;
  line-height: 54px;
</style>
