<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';
const ComLeaderStateCard = defineComponent({
  name: 'ComLeaderStateCard',
  components: {},
  props: {
    record: { type: Object, required: true },
    plus: { type: Boolean, default: false },
    index: { type: String, default: '' },
  },
  setup(props, { emit }) {
    // const today =ref(dayjs().format("MM月DD日"))

    const onDelete = () => emit('delete', props.record);
    console.log(props.record);
    return {
      ...toRefs(props),
      // today,
      onDelete,
    };
  },
});
export default ComLeaderStateCard;
</script>

<template lang="pug">
.com-leader-state-card.w-full.flex.justify-between.items-start(class="mb-[10px]")
  div.orderNum {{index+1}}.
  div.twelve(class="w-[85%]") {{record.payload?.日期}},{{record.payload?.领导动态}}
  div.delete.flex-grow.cursor-pointer(@click='onDelete',class="w-[12%]",v-if="plus") 删除
  div.flex-grow(v-if='!plus')
</template>

<style lang="stylus" scoped>
.orderNum
  font-size: 16px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #262626;
  line-height: 30px;
.delete
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #3B82F6;
  line-height: 18px;
.twelve
  font-size: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;

  line-height: 30px;
</style>
