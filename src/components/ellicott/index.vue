<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';
import { useRouter } from 'vue-router';

const ellicott = defineComponent({
  name: 'ellicott',
  components: {},
  props: {
    value: { type: String, default: 'copy' },
  },
  setup(props) {
    let msg = ref('');
    const router = useRouter();
    const skip = (str: string) => {
      router.push({
        path: '/home/<USER>',
        query: {
          website: str,
        },
      });
    };
    return {
      ...toRefs(props),
      msg,
      skip,
    };
  },
});
export default ellicott;
</script>

<template lang="pug">
.ellicottIndex
  //- iframe(class="iframe" src="https://www.baidu.com/" frameborder="0")
  .main
    .card(@click="skip('https://www.baidu.com')")
      img.img(src="https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/alc/alc6/news.jpg")
      .title  存志新闻云

    .card
      img.img(src="https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/alc/alc6/management.jpg")
      .title  存志管理云

    .card(@click="skip('http://**************/edv/index/class')")
      img.img(src="https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/alc/alc6/teaching.jpg")
      .title  存志教学云

    .card(@click="skip('https://www.hik-cloud.com/safe-center/index.html#/login/minerva')")
      img.img(src="https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/alc/alc6/IOT.jpg")
      .title  存志物联云

    .card.one
      img.img(src="https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/alc/alc6/courses.jpg")
      .title  存志课程云

    .card.therr
      img.img(src="https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/alc/alc6/tutor.jpg")
      .title  存志家校云
</template>

<style lang="stylus" scoped>
.ellicottIndex
  width 100%
  padding 50px 50px
  display flex
  justify-content center
  .main
    width 100%
    max-width 1260px
    display grid
    grid-template-columns 1fr 1fr 1fr 1fr
    grid-row-gap 24px
    grid-column-gap 20px
    grid-auto-rows 254px 290px
    .card
      width 300px
      position relative
      .title
        position absolute
        top 0
        left 0
        color #fff
        font-size 22px
        padding 20px
        font-family: PingFangSC-Regular, PingFang SC;
    .one
      width 620px
      grid-column 1/3
    .therr
      width 620px
      grid-column 3/5
  .img
    width 100%
    height 100%
</style>
