<script setup lang='ts'>
import Hls from 'hls.js';
import { onMounted, ref, onUnmounted } from 'vue';
import { message } from 'ant-design-vue';
const url = 'https://stiei-video.tallty.com/playlist.m3u8'
// const url = 'https://soa-storage.tallty.com/playlist.m3u8'
// const url = 'http://video.stiei.edu.cn/playlist.m3u8'
const config = {
  maxBufferSize: 200 * 1000 * 1000,
  maxBufferLength: 240,
  maxMaxBufferLength: 1800,
  // fragLoadingTimeOut: 8
}
const hls = new Hls(config);
const video = ref<any>(null)
onMounted(() => {
  if (!Hls.isSupported()) {
    console.log('HLS not supported')
  } else {
    if (video.value) {
      hls.loadSource(url);
      hls.attachMedia(video.value);
      hls.on(Hls.Events.MANIFEST_PARSED, function () {
        video.value.play();
      });
      hls.on(Hls.Events.ERROR, function (event, data) {
        console.log('Hls ERROR', data)
        var errorType = data.type;
        var errorDetails = data.details;
        var errorFatal = data.fatal;
        switch (data.details) {
          case Hls.ErrorDetails.FRAG_LOAD_ERROR:
            message.error('网络不佳');
            break;
          case "bufferStalledError":
            console.dir(video.value, 'video.value')
            video.value.currentTime = video.value.currentTime + 0.1;
            break;
          default:
            break;
        }
      });
    }
  }

})
onUnmounted(() => {
  hls.destroy();
})
</script>

<template lang="pug">

.com-video-js.w-1100px
  video.w-full.h-full(ref='video',:controls='true',:autoplay='true')
</template>
