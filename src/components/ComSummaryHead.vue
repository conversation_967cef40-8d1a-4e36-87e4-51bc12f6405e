<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';
const ComSummaryHead = defineComponent({
  name: 'ComSummaryHead',
  components: {},
  props: {
    value: { type: String, default: 'copy' },
    title: { type: String, default: '本周工作总结' },
    edit: { type: Boolean, default: false },
  },
  setup(props) {
    let msg = ref('');
    return {
      ...toRefs(props),
      msg,
    };
  },
});
export default ComSummaryHead;
</script>

<template lang="pug">
.com-summary-head.w-full.flex.justify-between
  div.title.relative.z-5 {{title}}
    div.rect.absolute.bottom-1.-z-1(:style="`background:${title==='本周工作总结'?'#A7F3D0':'#FED7AA'}`")
  div(v-if="edit")
    TaIcon(type="ToolOutlined",:size='15',color="#3B82F6")

</template>

<style lang="stylus" scoped>
.title
  font-size: 16px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #262626;
  line-height: 20px;
.rect
  width: 88px;
  height: 4px;
</style>
