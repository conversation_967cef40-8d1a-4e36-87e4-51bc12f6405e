<script lang="ts">
import { ref, defineComponent, toRefs, watch } from 'vue';
import dayjs, { Dayjs } from 'dayjs';
const ComWeekdayCard = defineComponent({
  name: 'ComWeekdayCard',
  components: {},
  props: {
    value: { type: String, default: 'copy' },
  },
  setup(props, { emit }) {
    const today = ref<Dayjs>(dayjs());
    const selectedDay = ref<Dayjs>(dayjs());
    const numberToChinese = (index: number) => {
      let a = ['日', '一', '二', '三', '四', '五', '六'];
      return a[index];
    };
    const weekdays = () => {
      emit('selectDay', selectedDay.value);
      let days = [selectedDay.value.startOf('week')];
      for (let i = 1; i < 7; i++) {
        days.push(days[i - 1].add(1, 'day'));
      }
      return days;
    };
    const kago = () => {
      selectedDay.value = selectedDay.value.subtract(7, 'day');
    };
    const milai = () => {
      selectedDay.value = selectedDay.value.add(7, 'day');
    };
    watch(
      () => selectedDay.value,
      () => weekdays(),
    );
    const which = ref(Number(today.value.format('d')));
    const pickDay = (index: number, day: Dayjs) => {
      selectedDay.value = day;
      return (which.value = index);
    };
    const pickToday = () => {
      selectedDay.value = today.value;
      which.value = Number(today.value.format('d'));
    }; //跳转到今日
    const toAll = () => {
      window.open('http://os.stiei.edu.cn/teaching/teacher/week_list');
    };
    return {
      ...toRefs(props),
      numberToChinese,
      dayjs,
      weekdays,
      kago,
      milai,
      pickDay,
      today,
      pickToday,
      which,
      selectedDay,
      toAll,
    };
  },
});
export default ComWeekdayCard;
</script>

<template lang="pug">
.com-weekday-card.w-full.flex.items-center.justify-between.mb-4
  .left.flex.flex-col.items-center
    .today.mb-2.cursor-pointer(@click='pickToday()') 今日
    .circle.flex.justify-center.items-center.cursor-pointer(@click='kago()')
      TaIcon(type='LeftOutlined',:size='12')
  .weekday.flex.space-x-1
    .date-box.flex.flex-col.justify-between.items-center.cursor-pointer(v-for='(day,index) in weekdays()',@click='pickDay(index,day)',:style='`background:${which===index?"#3F83F8":"#fff"};`')
      .date.mb-6px.text-gray-400.text-sm(:style='`color:${which===index?"#fff":""}`') {{day.format('DD')}}
      .week.text-gray-800.text-sm(:style='`color:${which===index?"#fff":""}`') {{numberToChinese(index)}}
  .right.flex.flex-col.items-center
    .all.mb-2(@click='toAll()') 全部
    .circle.flex.justify-center.items-center.cursor-pointer(@click='milai()')
      TaIcon(type='RightOutlined',:size='12')
.show
  .text-gray-800.font-medium.text-sm {{selectedDay.format('YYYY-MM-DD')}} 周{{numberToChinese(Number(selectedDay.format('d')))}}

</template>

<style lang="stylus" scoped>
.com-weekday-card
  .left
    .today
      color #4B5563
  .right
    .all
      color #4B5563
  .weekday
    .date-box
      border-radius 16px
      // background #3F83F8
      padding 8px 4px

.circle
  width 20px
  height 20px
  border 1px solid #9CA3AF
  border-radius 9999px
  transition .5s
  &:hover
    transform scale(1.05)
</style>
