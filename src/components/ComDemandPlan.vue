<script lang="ts">
import { ref, defineComponent, toRefs, computed, PropType } from 'vue';
import ComCardHead from '@/components/ComCardHead.vue';
import ComDemandPlanCard from '@/components/ComDemandPlanCard.vue';
import { VApi, VObject, VStore } from '@/lib/vails';
import { ReportItemModel } from '@/engines/report/report-core/models/report/user/execute/items';
import { ReportItem } from '@/types/model';
import { message } from 'ant-design-vue';
const ComDemandPlan = defineComponent({
  name: 'ComDemandPlan',
  components: { ComCardHead, ComDemandPlanCard },
  props: {
    value: { type: String, default: 'copy' },
    plus: { type: Boolean, default: false },
    token: { type: Object, required: true },
    rule: { type: Object, required: true },
    itemApi: { type: Object as PropType<VApi<ReportItem, string>>, required: true },
  },
  setup(props) {
    const title = '高层对接需求计划';

    const itemStore = new VStore(props.itemApi, ReportItemModel);
    const editRecord = ref<VObject>({});
    const fetchData = () => {
      itemStore.index().then(() => {
        // 创建
        editRecord.value = itemStore.new({
          payload: {},
        });
      });
    };
    fetchData();
    const onFormConfirm = () => {
      editRecord.value
        .save()
        .then(() => {
          message.success('创建成功');
          fetchData();
        })
        .catch(() => {
          message.error('创建失败');
        });
    };
    const config = computed(() => ({
      store: itemStore,
      mode: 'list',
      template: props.rule.form?.rule_form,
      scrollLoading: true,
      actions: [
        {
          key: 'create',
          enabled: false,
        },
        {
          key: 'export',
          enabled: false,
        },
        {
          key: 'import',
          enabled: false,
        },
      ],
    }));
    return {
      ...toRefs(props),
      title,
      config,
      editRecord,
      onFormConfirm,
    };
  },
});
export default ComDemandPlan;
</script>

<template lang="pug">
.com-demand-plan.w-full.px-5
  TaIndexView(:config='config')
    template(#header)
      .header.w-full.bg-white
        ComCardHead.mb-5.pt-5(:title='title',:plus='plus')
    template(#card='{ record, actions,index }')
      ComDemandPlanCard(:record='record', @delete='actions.onDelete',:editRecord='editRecord',:plus='plus',:index='index')
    template(#right-actions='{ actions }')
      TaSvg.primary-color.cursor-pointer.mb-5(
        src='https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/tailwind-icons/outline/plus-circle.svg',
        @click='actions.onCreate',
        v-if="plus"
      )
</template>

<style lang="stylus" scoped>
.primary-color
  color $primary-color
</style>
