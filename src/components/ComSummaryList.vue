<script lang="ts">
import { ref, defineComponent, toRefs, PropType } from 'vue';
import ComCardHead from '@/components/ComCardHead.vue';
import ComSummaryHead from '@/components/ComSummaryHead.vue';
import { ReportItem } from '@/types/model';
import { VApi, VStore, VObject } from '@/lib/vails';
import { ReportItemModel } from '@/engines/report/report-core/models/report/user/execute/items';
import { message } from 'ant-design-vue';
import ComSummary from '@/components/ComSummary.vue';

const ComSummaryList = defineComponent({
  name: 'ComSummaryList',
  components: { ComCardHead, ComSummaryHead, ComSummary },
  props: {
    editable: { type: Boolean, default: false },
    head: { type: String, default: '工作总结/计划' },

    // token: { type: Object, required: true },
    rule: { type: Object, required: true },
    itemApi: { type: Object as PropType<VApi<ReportItem, string>>, required: true },
  },
  setup(props) {
    const itemStore = new VStore(props.itemApi, ReportItemModel);

    const fetchData = () => {
      itemStore.index();
    };

    fetchData();

    return {
      ...toRefs(props),
      records: itemStore.records,
      fetchData,
    };
  },
});
export default ComSummaryList;
</script>

<template lang="pug">
.com-summary-list.w-full.py-5.pr-5.pl-2
  ComSummary.mb-5(
    v-for='record in records',
    :record='record',
    :head='record.user?.name',
    :rule='rule',
    :editable='editable',
    @fetch='fetchData'
  )
</template>

<style lang="stylus" scoped>
.primary-color
  color $primary-color
.line
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #737373;
  line-height: 21px;
</style>
