<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';
const ComCardHead = defineComponent({
  name: 'ComCardHead',
  components: {},
  props: {
    value: { type: String, default: 'copy' },
    title: { type: String, default: 'copy' },
  },
  setup(props) {
    let msg = ref('');
    return {
      ...toRefs(props),
      msg,
    };
  },
});
export default ComCardHead;
</script>

<template lang="pug">
.com-head-card.flex.w-full.justify-between
  div.flex
    div.shape.mr-3
    div.title {{title}}
  slot
    //- TaIcon(type='PlusCircleOutlined',:size='15',color='#3B82F6')
</template>

<style lang="stylus" scoped>
.shape
  width: 8px;
  height: 24px;
  background: #BFDBFE;
  border-radius: 7px;
.title
  font-size: 20px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #18181B;
  line-height: 24px;
</style>
