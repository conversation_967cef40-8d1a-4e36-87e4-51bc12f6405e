<script lang="ts">
import { ref, defineComponent, toRefs, watch, computed } from 'vue';
const ComPersonalCenterCard = defineComponent({
  name: 'ComPersonalCenterCard',
  components: {},
  props: {
    record: { type: Object, default: () => {} },
    userInfo: { type: Object, default: () => {} },
    infoRecord: { type: Object, default: () => {} },
  },
  setup(props) {
    const config = computed(() =>
      props.userInfo?.members?.[0]?.member_type === 'Teacher'
        ? [
            {
              icon: 'clock',
              name: '我的待办',
              url: 'http://os.stiei.edu.cn/portal/all_instances',
            },
            // {
            //   icon: 'chat-alt-2',
            //   name: '我的会议',
            //   url: 'https://os.stiei.edu.cn/conference/teacher/activities/weekly',
            // },
            {
              icon: 'mail',
              name: '我的邮件',
              url: 'http://mailauth.stiei.edu.cn/',
            },
          ]
        : [
            {
              icon: 'clock',
              name: '我的待办',
              url: 'http://os.stiei.edu.cn/portal/all_instances',
            },

            {
              icon: 'mail',
              name: '我的邮件',
              url: 'http://mailauth.stiei.edu.cn/',
            },
          ],
    );

    const toLink = (url: string) => {
      window.open(url);
    };

    return {
      ...toRefs(props),
      config,
      toLink,
    };
  },
});
export default ComPersonalCenterCard;
</script>

<template lang="pug">
.com-personal-center-card.w-full
  .top.py-4.w-full.relative(@click='toLink("https://os.stiei.edu.cn/auth/info")')
    .content.flex.items-center.px-4
      .circle.w-10.h-10.rounded-full.mr-2.flex.items-center.justify-center.flex-shrink-0
        .text-white.text-base {{ (infoRecord?.name || 'no').slice(-2) }}
        //- (v-if='userInfo.name') {{userInfo.name?backTwo(userInfo.name):'no'}}
      .info
        .name.text-base.text-gray-800.mb-1(:phoneNumber='`${userInfo?.account}`') {{ userInfo?.name }}
        .college.text-xs.text-gray-800 {{ userInfo?.department_names?.[0] || infoRecord?.department_name }}
  .bottom.py-4
    .config.flex.justify-around.px-4
      .part.flex.flex-col.items-center.cursor-pointer(
        v-for='part in config',
        @click='toLink(part.url)'
      )
        TaIcon.w-6.h-6.mb-2(:type='`outline/${part.icon}`')
        .part__name {{ part.name }}
</template>

<style lang="stylus" scoped>
.com-personal-center-card {
  box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1), 0px 1px 2px -1px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  background: url('https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/school/Frame%20265%20%281%29.png'), linear-gradient(0deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.4)), #FFFFFF; // stiei-obs2.obs.cn-east-2.myhuaweicloud.com/school/Frame%20265%20%281%29.png), linear-gradient(0deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.4)), #FFFFFF;
  background-size: contain;
  background-repeat: no-repeat;

  .top {
    border-bottom: 1px solid #F3F4F6;
    pointer-events: none; // 为了给伪元素加点击事件，取消掉父元素的点击事件

    &::after {
      content: '个人中心';
      background: #3F83F8;
      position: absolute;
      pointer-events: auto;
      cursor: pointer;
      right: 0;
      top: 16px;
      border-radius: 16px 0px 0px 16px;
      height: 28px;
      padding: 4px 8px;
      color: #fff;
      font-size: 14px;
      line-height: 20px;
    }

    .content {
      pointer-events: none;

      .circle {
        background-color: #1890ff;
      }

      .info {
        .name {
          font-weight: 500;

          &::after {
            content: attr(phoneNumber);
            margin-left: 8px;
            color: #9CA3AF;
            font-size: 12px;
            font-weight: 400;
            font-style: normal;
            line-height: 150%;
            font-family: 'PingFang SC';
          }
        }
      }
    }
  }
}
</style>
