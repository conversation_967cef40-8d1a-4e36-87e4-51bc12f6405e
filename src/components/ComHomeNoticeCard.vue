<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';
import dayjs from 'dayjs';
const ComHomeNoticeCard = defineComponent({
  name: 'ComHomeNoticeCard',
  components: {},
  props: {
    mode: { type: Boolean, default: false },
    record: { type: Object, default: () => { } },
  },
  setup(props) {
    return {
      ...toRefs(props),
      dayjs,
    };
  },
});
export default ComHomeNoticeCard;
</script>

<template lang="pug">
.com-home-notice-card.flex.pb-4.w-full.cursor-pointer
  a-tooltip(:title='t = `【${record?.catalog}】${record?.title}`')
    .title.text-gray-800.text-sm.font-medium.truncate.flex-grow.w-0.mr-2 {{ t}}
  .time.text-gray-400.text-xs {{dayjs(record?.publish_time).format('YY-MM-DD')}}
  //- .top.flex.justify-between.mb-2
  //-   .left.space-y-2
  //-     .title.text-gray-800.text-sm.font-medium {{`【${record?.catalog}】${record?.title}`}}
  //-     //- .content.h-42px.text-gray-600.text-sm.two-line-text {{record?.content}}

  //-   .right.flex.items-start.ml-4(v-if='mode')
  //-     .img-box.w-25.h-75px
  //-       img.w-25.h-75px.rounded-md(:src='`${record.cover_image?record.cover_image:"https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/school/Rectangle%2034624117.png"}`',onerror="javascript:this.src='https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/school/Rectangle%2034624117.png'")
  //- .bottom.flex.justify-between.items-center
  //-   .college.text-gray-400.text-xs
  //-   .time.text-gray-400.text-xs {{dayjs(record?.publish_time).format('YY-MM-DD HH:mm')}}

</template>

<style lang="stylus" scoped>
.com-home-notice-card
  border-bottom 1px solid #E5E7EB
  &:hover
    .title
      color #3F83F8
.title
  transition all .8s
</style>
