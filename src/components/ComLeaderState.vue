<script lang="ts">
import { ref, defineComponent, toRefs, computed, PropType } from 'vue';
import ComCardHead from '@/components/ComCardHead.vue';
import ComLeaderStateCard from '@/components/ComLeaderStateCard.vue';
import { VStore, VApi, VObject } from '@/lib/vails';
import { ReportItemModel } from '@/engines/report/report-core/models/report/user/execute/items';
import { ReportItem } from '@/types/model';
import { message } from 'ant-design-vue';
import usePolicy from './global/ta-component/ta-template-form-core/usePolicy';

const ComLeaderState = defineComponent({
  name: 'ComLeaderState',
  components: { ComCardHead, ComLeaderStateCard },
  props: {
    plus: { type: Boolean, default: false },
    token: { type: Object, required: true },
    rule: { type: Object, required: true },
    itemApi: { type: Object as PropType<VApi<ReportItem, string>>, required: true },
    // submittable: { type: Boolean, default: false },
  },
  setup(props) {
    const title = '公司领导动态';
    usePolicy();
    const itemStore = new VStore(props.itemApi, ReportItemModel);
    const editRecord = ref<VObject>({});
    const fetchData = () => {
      itemStore.index().then(() => {
        // 创建
        editRecord.value = itemStore.new({
          payload: {},
        });
      });
    };
    fetchData();
    const onFormConfirm = () => {
      editRecord.value
        .save()
        .then(() => {
          message.success('创建成功');
          fetchData();
        })
        .catch(() => {
          message.error('创建失败');
        });
    };
    const config = computed(() => ({
      store: itemStore,
      mode: 'list',
      template: props.rule.form?.rule_form,
      scrollLoading: true,
      actions: [
        {
          key: 'create',
          enabled: false,
        },
        {
          key: 'export',
          enabled: false,
        },
        {
          key: 'import',
          enabled: false,
        },
      ],
    }));

    return {
      ...toRefs(props),
      title,
      config,
      editRecord,
      onFormConfirm,
      itemStore,
    };
  },
});
export default ComLeaderState;
</script>

<template lang="pug">
.com-leader-state.w-full.px-5
  TaIndexView.over(:config='config')
    template(#header)
      .header.w-full.bg-white
        ComCardHead.mb-5.pt-5.bg-white(:title='title')
    template(#card='{ record, actions,index }')
      ComLeaderStateCard(:record='record', @delete='actions.onDelete',:plus='plus',:editRecord='editRecord',:index='index')
    template(#right-actions='{ actions }')
      TaSvg.primary-color.cursor-pointer.mb-5.w-6.h-6(
        src='https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/tailwind-icons/outline/plus-circle.svg',
        @click='actions.onCreate'
        v-if="plus"
      )
</template>

<style lang="stylus" scoped>
.primary-color
  color $primary-color
.com-leader-state
  .over
    >>>.empty-placeholder
      display none
// .over
//   overflow: hidden
</style>
