<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';
import dayjs from 'dayjs';
const ComScheduleCard = defineComponent({
  name: 'ComScheduleCard',
  components: {},
  props: {
    color: { type: String, default: '#3F83F8' },
    bg: { type: String, default: '#EBF5FF' },
    record: { type: Object, required: true },
  },
  setup(props) {
    // console.log(props);
    return {
      ...toRefs(props),
      dayjs,
    };
  },
});
export default ComScheduleCard;
</script>

<template lang="pug">
.com-schedule-card.w-full.pl-22px.pr-4.flex.relative(:style='`background-color:${bg}`')
  .content.w-full.my-2.flex.items-center.justify-between(:style='`--color:${color}`')
    .left
      .name.text-gray-800.text-sm.font-medium.mb-2.leading-normal {{record?.course_set_name}}
      .condition.flex.items-center
        TaIcon.ta-icon.w-4.h-4.text-gray-400.mr-1(type='solid/location-marker',:size='10')
        .classroom.text-gray-400.text-sm.leading-normal {{record?.classroom_name}}
    .right.text-gray-400.text-sm.leading-normal {{dayjs(record?.start_datetime).format('HH:mm')}}-{{dayjs(record?.end_datetime).format('HH:mm')}}

</template>

<style lang="stylus" scoped>
.opacity-40
  &:hover::before
    display none
.com-schedule-card
  &:hover::before
    content ""
    position absolute
    top 50%;left 5%;right 7%;bottom 0
    background hsl(210,100%,20%)
    transform translate(0, -25%) rotate(-2deg)
    transform-origin center center
    box-shadow 0 0 6px 10px hsl(210,100%,20%)
    z-index -1
  .content
    &::before
      content ''
      position absolute
      width 6px
      height 100%
      background-color var(--color)
      border-radius 15px
      display inline-block
      left -2px;top 0
</style>
