<script lang="ts">
import { ref, defineComponent, toRefs, watch, computed, onMounted, watchEffect } from 'vue';
import ComScheduleCard from './ComScheduleCard.vue';
import ComWeekdayCard from './ComWeekdayCard.vue';
import dayjs, { Dayjs } from 'dayjs';
const ComScheduleCardList = defineComponent({
  name: 'ComScheduleCardList',
  components: { ComScheduleCard, ComWeekdayCard },
  props: {
    userInfo: { type: Object, default: () => { } },
    lessonStore: { type: Object, default: () => { } },
    // studentLessonStore: { type: Object, default: () => {} },
  },
  setup(props) {
    const userType = ref('');
    const config = computed(() => ({
      store: props.lessonStore,
      mode: 'list',
      pagination: { hide: true, perPage: 4 },
      params: {
        q: { date_eq: selectedDay.value.format('YYYY-MM-DD') },
      },
      // list: {
      //   gap: 4,
      // },
    }));

    watchEffect(() => userType.value = props.userInfo.members?.[0]?.member_type)
    const selectedDay = ref<Dayjs>(dayjs());
    const selectDay = (sDay: Dayjs) => {
      console.log(sDay, 'sDay');
      selectedDay.value = sDay;
    };
    const checklesson = (record: any) => {
      window.open(
        `https://os.stiei.edu.cn/teaching/${userType.value === 'Teacher' ? 'teacher' : 'student'
        }/courses/${record.course_id}/lessons/${record.id}`,
      );
    };
    return {
      ...toRefs(props),
      config,
      selectDay,
      selectedDay,
      dayjs,
      checklesson,
    };
  },
});
export default ComScheduleCardList;
</script>

<template lang="pug">
.com-schedule-card-list.w-full.relative.pt-4.pb-2
  .blue-line.absolute
  .time-select.px-3.mb-4
    ComWeekdayCard(@selectDay='selectDay')
  .schedule-list.px-3
    TaIndexView.ta-index-view(:config='config')
      template(#empty)
        .w-full.text-gray-500.text-xs.text-center.py-6 暂无数据
      template(#card='{record}')
        ComScheduleCard.mb-2.cursor-pointer(
          :record='record',
        :class='`${selectedDay.isBefore(dayjs(),"day")?"opacity-40":""}`',
        :bg='`${record.start_unit>=5?"#FFF8F1":"#EBF5FF"}`',
        :color='`${record.start_unit>=5?"#FF8A4C":"#3F83F8"}`',
        @click='checklesson(record)'
        )
</template>

<style lang="stylus">
.com-schedule-card-list
  background #FFFFFF
  box-shadow: 0px 1px 2px -1px rgba(0, 0, 0, 0.1);
  filter: drop-shadow(0px 1px 3px rgba(0, 0, 0, 0.1));
  border-radius: 8px;
  .blue-line
    width 300px
    height 4px
    top 0;left 0;
    background #3F83F8
    border-radius 9px 9px 0px 0px
  .schedule-list
    .ta-index-view
      .ta-index-view-header
        height 0 !important
      .list-view__pagination_placeholder
       height 0 !important
</style>
