<script lang="ts">
import { ref, defineComponent, toRefs, onMounted, computed } from 'vue';
import ComBasicInfoCard from '@/engines/research/components/research/entries/ComBasicInfoCard.vue';
import ComAcitvitiesStepsCard from '@/engines/research/components/research/entries/ComAcitvitiesStepsCard.vue';
import ComBpmInstanceDetailDialog from '@/engines/bpm/components/ComBpmInstanceDetailDialog.vue';
const ResearchEntryFlowable = defineComponent({
  name: 'ResearchEntryFlowable',
  components: {
    ComBasicInfoCard,
    ComAcitvitiesStepsCard,
  },
  props: {
    record: { type: Object, required: true },
  },
  setup(props) {
    const entry = computed(() => props.record);
    console.log(entry.value, 'entry');
    const toShow = (id: any) => {
      window.open(`/education/research/user/entries/${id}`);
    };
    return {
      ...toRefs(props),
      entry,
      toShow,
    };
  },
});
export default ResearchEntryFlowable;
</script>

<template lang="pug">
.com-stage-main
  ComBasicInfoCard.cursor-pointer(v-if="record.id" :record="entry" @click='toShow(record.flowable_info.id)')
  //- ComBpmInstanceDetailDialog(v-if='record.id' :instanceId='record.current_token?.instance_id',v-model:visible='visible')
  //- .stage-main-head
  //-   .steps-card(v-for="(item,index) in entry.stages")
  //-     div(:class="record.id === item.instance.id ? 'divDisplay':''")
  //-       ComAcitvitiesStepsCard(v-if="record.id" :record="item" :entry="entry")
</template>

<style lang="stylus" scoped>
// .com-stage-main
//   .stage-main-head
//     margin-top 24px
//     display grid
//     grid-template-columns: 1fr 1fr 1fr;
//     grid-gap 24px
//   .divDisplay
//     box-shadow: 5px 5px 5px rgba(127,165,243,0.2)
</style>
