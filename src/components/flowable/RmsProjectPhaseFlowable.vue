<script lang="ts">
import { defineComponent, toRefs, computed } from 'vue';
import ComBasicInfoCard from '@/engines/rms/components/rms/projects/ComBasicInfoCard.vue';
import ComAcitvitiesStepsCard from '@/engines/research/components/research/entries/ComAcitvitiesStepsCard.vue';
const RmsProjectPhaseFlowable = defineComponent({
  name: 'RmsProjectPhaseFlowable',
  components: {
    ComBasicInfoCard,
    ComAcitvitiesStepsCard,
  },
  props: {
    record: { type: Object, required: true },
  },
  setup(props) {
    const entry = computed(() => props.record.flowable_info || {});
    console.log(entry.value, 'entry');

    return {
      ...toRefs(props),
      entry,
    };
  },
});
export default RmsProjectPhaseFlowable;
</script>

<template lang="pug">
.com-stage-main
  ComBasicInfoCard(v-if='record.id', :record='record')
  //- .stage-main-head
  //-   .steps-card(v-for='(item, index) in entry.stages')
  //-     div(:class='record.id === item.instance.id ? "divDisplay" : ""')
  //-       ComAcitvitiesStepsCard(v-if='record.id', :record='item', :entry='entry')
</template>

<style lang="stylus" scoped>
.com-stage-main {
  .stage-main-head {
    margin-top: 24px;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-gap: 24px;
  }

  .divDisplay {
    box-shadow: 5px 5px 5px rgba(127, 165, 243, 0.2);
  }
}
</style>
