<script lang="ts">
import { ref, defineComponent, toRefs, onMounted, computed } from 'vue';
import ComBasicInfoCard from '@/engines/research/components/research/entries/ComBasicInfoCard.vue';
import ComAcitvitiesStepsCard from '@/engines/research/components/research/entries/ComAcitvitiesStepsCard.vue';
const ResearchEntryFlowable = defineComponent({
  name: 'ResearchEntryFlowable',
  components: {
    ComBasicInfoCard,
    ComAcitvitiesStepsCard,
  },
  props: {
    record: { type: Object, required: true },
  },
  setup(props) {
    const entry = computed(() => props.record.flowable_info || {});
    console.log(entry.value, 'entry');

    const fields = [
      { label: '专业技术资格1', key: '专业技术资格1' },
      { label: '职称级别1', key: '职称级别1' },
      { label: '职称资格时间1', key: '职称资格时间1' },
      { label: '专业技术资格2', key: '专业技术资格2' },
      { label: '职称级别2', key: '职称级别2' },
      { label: '职称资格时间2', key: '职称资格时间2' },
      { label: '专技岗位晋级时间', key: '专技岗位晋级时间' },
      { label: '职业资格等级', key: '职业资格等级' },
      { label: '职业资格取得时间', key: '职业资格取得时间' },
      { label: '职业资格证书名称', key: '职业资格证书名称' },
      { label: '职称级别1', key: '职称级别1' },
      { label: '职称资格时间1', key: '职称资格时间1' },
      { label: '职称级别2', key: '职称级别2' },
      { label: '职称资格时间2', key: '职称资格时间2' },
      { label: '岗位', key: '岗位' },
      { label: '岗位等级', key: '岗位等级' },
      { label: '岗位类别', key: '岗位类别' },
      { label: '研究方向', key: '研究方向' },
      { label: '一级学科', key: '一级学科' },
      { label: '二级学科', key: '二级学科' },
      { label: '三级学科', key: '三级学科' },
    ];

    return {
      ...toRefs(props),
      entry,
      fields,
    };
  },
});
export default ResearchEntryFlowable;
</script>

<template lang="pug">
.com-stage-main
  ComBasicInfoCard(v-if='record.id', :record='record')
  //- .card.boder.border-blue-200.rounded-lg.p-6.w-full.bg-blue-50
  //-   h3.text-blue-600.text-lg.font-bold.mb-4 申报人信息
  //-   .grid.grid-cols-3.items-center
  //-     .font-medium(v-for='(item,index) in fields', :key='index')
  //-       span.text-blue-400 {{ item.label }}:
  //-       span.ml-2.text-gray-600 {{ record.creator_user?.model_payload ? record.creator_user?.model_payload[item.key] : '' }}
  .stage-main-head
    .steps-card(v-for='(item, index) in entry.stages')
      div(:class='record.id === item.instance.id ? "divDisplay" : ""')
        ComAcitvitiesStepsCard(v-if='record.id', :record='item', :entry='entry')
</template>

<style lang="stylus" scoped>
.com-stage-main {
  .stage-main-head {
    margin-top: 24px;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-gap: 24px;
  }

  .divDisplay {
    box-shadow: 5px 5px 5px rgba(127, 165, 243, 0.2);
  }
}
</style>
