<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';
import ComWaitListCard from './ComWaitListCard.vue';
import ComMoreButton from '../engines/research/components/research/entries/ComMoreButton.vue';
import { VStore } from '@/lib/vails';
import ComBpmInstanceDetailDialog from '@/engines/bpm/components/ComBpmInstanceDetailDialog.vue';
const ComWaitCardList = defineComponent({
  name: 'ComWaitCardList',
  components: { ComWaitListCard, ComMoreButton, ComBpmInstanceDetailDialog },
  props: {
    store: { type: Object, required: true },
    userInfo: { type: Object, default: () => {} },
    infoRecord: { type: Object, default: () => {} },
    studentInfo: { type: Object, default: () => {} },
    mode: { type: String, default: 'campus' },
    url: { type: String, default: 'http://os.stiei.edu.cn/portal/all_instances' },
  },
  setup(props) {
    const id = computed(() => {
      return props.infoRecord?.id;
    });
    const config = computed(() => ({
      store: props.store,
      mode: 'list',
      pagination: { hide: true, perPage: 5 },
      // scrollLoading: true,
      params:
        props.mode === 'hr'
          ? {}
          : {
              q: {
                approving: [id.value, props.userInfo?.members?.[0]?.member_type],
              },
            },
      list: {
        gap: 16,
      },
      // detail: {
      //   // mode: 'route',
      //   // url: 'http://os.stiei.edu.cn/portal/all_instances',
      // },
    }));
    const instanceId = ref(0);
    const visible = ref(false);
    const toWaitList = () => {
      window.open('https://os.stiei.edu.cn/portal/all_instances');
    };
    return {
      ...toRefs(props),
      config,
      visible,
      toWaitList,
      instanceId,
    };
  },
});
export default ComWaitCardList;
</script>

<template lang="pug">
.com-wait-card-list.w-full.p-4.relative
  .blue-line.absolute
  .head.text-sm.text-gray-800.font-medium.mb-4 我的待办
  .blue-subhead.w-full.flex.justify-between.items-center.px-4.py-2.mb-4
    .circle.rounded-full.bg-white.flex.items-center.justify-center.w-7.h-7
      TaIcon.w-5.h-5.text-blue-500(type='solid/clipboard-check')
    .circle.rounded-full.bg-white.flex.items-center.justify-center.h-5.px-6px
      .num.text-blue-500.text-xs {{store.totalCount}}
  .list.w-full.mb-4
    TaIndexView.ta-index-view(:config='config')
      template(#card='{record}')
        ComWaitListCard.cursor-pointer(:record='record',@click='toWaitList')
  .more.flex.justify-end
    ComMoreButton(:url='url')
//- ComBpmInstanceDetailDialog(v-if='instanceId',:instanceId='instanceId',v-model:visible='visible')
</template>

<style lang="stylus">
.com-wait-card-list
  background #FFFFFF
  box-shadow 0px 1px 3px rgba(0, 0, 0, 0.1), 0px 1px 2px -1px rgba(0, 0, 0, 0.1)
  border-radius 0px 0px 8px 8px
  .blue-line
    width 300px
    height 4px
    top 0;left 0;
    background #3F83F8
    border-radius 9px 9px 0px 0px
  .blue-subhead
    background #3F83F8
    border-radius 8px
  .list
    .ta-index-view
      .ta-index-view-header
        height 0 !important
        .header
          .table-header
            height 0 !important
      .list-view__pagination_placeholder
        height 0 !important
</style>
