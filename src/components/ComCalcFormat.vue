<script lang='ts'>
import { ref, defineComponent, toRefs, computed, unref, watch, nextTick } from 'vue';
import { TaTemplateFormItemMergeOptions } from './global/ta-component/ta-template-form-core/useProcessFields';
const ComCalcFormat = defineComponent({
  name: 'ComCalcFormat',
  components: {},
  props: {
    value: { type: String, default: '0' },
    data: { type: Object, default: () => [] }
  },
  setup(props, { emit }) {
    const input = ref<any>(null)
    const fakeData = computed(() => props.data)
    const calcKeyBoardData: any[] = [
      '(', ')', '%', 'AC', '7', '8', '9', '÷', '4', '5', '6', '×', '1', '2', '3', '-', '0', '.', '=', '+'
    ]
    const operSymbol = ['+', '-', '×', '÷', '=']
    const brackets = ['(', ')']

    const bracketsLevel = ref<number>(0)


    const localValue = computed({
      get() {
        return props.value.replaceAll('*', '×').replaceAll("/", "÷")
      },
      set(val: any) {
        emit('update:value', val.replaceAll('×', '*').replaceAll('÷', '/'))
      }
    })
    const showFormat = computed(() => {
      return bracketsLevel.value > 0 ? `(${localValue.value})` : localValue.value ||= '0'
    })


    const keyStrategy: any = {
      'AC': (key: string) => {
        bracketsLevel.value = 0
        return localValue.value = '0'
      },
      '%': (key: string) => {
        const formatLast = localValue.value?.substring(localValue.value?.length - 1)
        if (operSymbol.includes(formatLast)) {
          return localValue.value = `${localValue.value.substring(0, localValue.value?.length - 1)}%`
        }
        localValue.value = `${localValue.value}${key}`
      },
      '(': (key: string) => {
        bracketsLevel.value = 1
        //TODO 更好地支持嵌套括号,使level与嵌套数对应
      },
      ')': (key: string) => {
        if (bracketsLevel.value === 1) {
          localValue.value = unref(showFormat.value)
          bracketsLevel.value = 0
        }
      },
      //TODO 其他特殊操作
    }
    const onTap = (key: string) => {
      if (localValue.value === '0' &&
        key !== 'AC' &&
        !brackets.includes(key) &&
        !operSymbol.includes(key) &&
        key !== '%'
      ) return localValue.value = key;
      if (operSymbol.includes(key)) return localValue.value = `${localValue.value} ${key} `;
      if (Object.keys(keyStrategy).includes(key)) return keyStrategy[key](key)

      localValue.value = `${localValue.value}${key}`
    }

    //使输入部分总是可以被看见而不会被隐藏
    watch(() => showFormat.value, () => {
      nextTick(() => {
        if (input.value) {
          input.value.scrollLeft = input.value.scrollWidth
        }
      })
    })
    return {
      ...toRefs(props),
      calcKeyBoardData,
      onTap,
      showFormat,
      fakeData,
      input,
    };
  },
});
export default ComCalcFormat;
</script>

<template lang="pug">
view.com-calc-format.w-666px.p-2
  .calc__container.w-full
    .calc__screen.w-full.h-72px.mb-1
      .h-20px.w-full.flex.justity-between.relative
        .icon__container.w-5.h-5
          TaIcon.ta-icon(type='solid/clock')
          .history__container.absolute
      .calc__input.w-full.text-right(ref='input') {{ showFormat }}
  .calc__keyboard.w-full.mt-2.flex
    .calc__objects.flex-grow.flex.mr-2.gap-y-2.flex-col
      .calc__object.w-full.flex.items-center.justify-center.text-white.flex-shrink-0(v-for='i in fakeData',@click='onTap(i)',:class="{overShirnk:i?.length > 15}") {{ i }}
    .calc__core.grid-cols-4.grid.gap-2.flex-shrink-0
      .calc__key(v-for='key in calcKeyBoardData',:id='`c${key}`',@click='onTap(key)') {{ key }}
</template>

<style lang="stylus" scoped>
::-webkit-scrollbar
  display none
.com-calc-format
  display block
  background #202124
  .calc__container
    .calc__screen
      border-radius 8px
      border 1px solid #3c4043
      padding 6px 14px 0 10px
      transition all .3s
      &:hover
        background #303134
        border 1px solid @background

      .icon__container
        .ta-icon
          color #5f6368
        &:hover
          .history__container
            position absolute
            left 0;top 0;
            width 100%
            height 200px
            background #202124
      .calc__input
        font-size 30px
        height 32px
        color white
        overflow-x auto
        white-space nowrap
        line-height 32px

  .calc__keyboard
    .calc__objects
      overflow auto
      height 212px
      .calc__object
        background #5f6368
        border-radius 8px
        cursor pointer
        transition all .3s
        letter-spacing .2em
        height 40px
        &:hover
          background #70757a
      .overShirnk
        @apply px-2;
    .calc__core
      width percentage(4/7)
      .calc__key
        border-radius 4px
        background #5f6368
        border 1px solid @background
        font-size 18px
        color #e8eaed
        line-height 34px
        text-align center
        cursor pointer
        transition all .3s
        &:hover
          background #70757a
        &:nth-child(4n+5),&:nth-child(4n+6),&:nth-child(4n+7)
          background #3c4043
          border 1px solid @background
          &:hover
            background #424548
            border 1px solid @background
        &:nth-child(19)
          background #8ab4f8
          border 1px solid @background
          color #202124
          &:hover
            background #aecbfa
            border 1px solid @background



</style>
