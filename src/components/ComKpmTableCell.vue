<script lang='ts'>
import { ref, defineComponent, toRefs, computed, watch } from 'vue';
import { VStore } from '../lib/vails/store/index';
import { PerfAdminTargetsApi } from '../engines/perf/perf-core/apis/perf/admin/targets.api';
const ComKpmTableCell = defineComponent({
  name: 'ComKpmTableCell',
  components: {},
  props: {
    record: { type: Object, required: true },
    column: { type: Object, default: () => { } },
    editable: { type: Boolean, default: true },
    index: { type: Number, default: 0 },
  },
  setup(props) {
    const targetStore = new VStore(new PerfAdminTargetsApi({}));
    const data = computed(() => props.record.tokens[props.column?.key].target.option.vals || [0])
    const bool = ref<boolean>(props.record.tokens[props.column.key].target.state === "closed" ? false : true)
    const value = computed({
      get: () => {
        return bool.value
      },
      set: (val) => {
        bool.value = !bool.value
      }
    })

    watch(() => bool.value, () => {
      const token = props.record.tokens[props.column.key]
      targetStore
        .find(token.target.id).then(() => {
          targetStore.record.value.update({ state: bool.value ? "actived" : "closed" })
        })
    })
    return {
      ...toRefs(props),
      data,
      value,
      bool,
    };
  },
});
export default ComKpmTableCell;
</script>

<template lang="pug">
view.com-kpm-table-cell.w-full.px-4.py-1.flex.gap-x-1.items-center
  .cell__squ.flex.items-center.gap-x-1.p-2(v-for='(item,i) in data',v-show='bool')
    .text-sm {{ item.val }}
    TaIcon.w-3.h-3.text-gray-500(type='solid/x')
  .cell__add.w-5.h-5.bg-gray-100.rounded-full.flex.justify-center.items-center.cursor-pointer(v-show='bool')
    TaIcon.text-blue-700.w-14px.h-14px(type='solid/plus-circle')
  TaSwitch(v-model:value='value',:item='{}',@click.stop)
</template>

<style lang="stylus" scoped>
.com-kpm-table-cell
  .cell__squ
    border-radius 8px
    border 1px solid var(--gray-300, #D1D5DB)
    background var(--gray-50, #F9FAFB)
</style>
