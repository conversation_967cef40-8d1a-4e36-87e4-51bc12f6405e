<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';
import { useScreenDataFetchCollectionInject } from './global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
const simpleFlipCard = defineComponent({
  name: 'simpleFlipCard',
  components: {},
  props: {
    num: { type: Number, default: 0 },
  },
  setup(props) {
    const { getScreenDataByKey } = useScreenDataFetchCollectionInject(props);
    const initFrontNum = ref(0);
    const digit = (num: number) => {
      let l = 0;
      if (num >= 1) {
        while (num >= 1) {
          num = num / 10;
          l++;
        }
      } else {
        l = 1;
      }

      console.log(l);
      return l;
    };
    return {
      ...toRefs(props),
      digit,
    };
  },
});
export default simpleFlipCard;
</script>

<template lang="pug">
.simple-flip-card(:style='`--w:${digit(45)*49}px`')
  .flip-card-inner
    .flip-card-front.flex.items-center.justify-center
      .text 45
    .flip-card-back.flex.items-center.justify-center
      .text 54
</template>

<style lang="stylus" scoped>
.simple-flip-card
  background-color transparent
  width var(--w)
  height 80px
  border 1px solid #f1f1f1
  perspective 1000px
  .flip-card-inner
    position relative
    width 100%
    height 100%
    text-align center
    transition transform 0.8s
    transform-style preserve-3d
    .text
      font-size 64px
      font-family 'DS-Digital'
      font-style normal
      font-weight 700
      font-size 64px
      line-height 100%
.simple-flip-card:hover .flip-card-inner
    transform rotateX(180deg)
.flip-card-front, .flip-card-back
  position absolute
  width 100%
  height 100%
  -webkit-backface-visibility hidden
  backface-visibility hidden
  border-radius 4px

.flip-card-front
  background-color red
  color #8AE9ED

.flip-card-back
  color #8AE9ED
  background-color red
  transform rotateX(180deg)

// #174269
// #2b618a
</style>
