<script lang="ts">
import { ref, defineComponent, toRefs, computed, watch } from 'vue';
import ComHomeNoticeCard from '@/components/ComHomeNoticeCard.vue';
import ComMoreButton from '@/engines/research/components/research/entries/ComMoreButton.vue';
import { InformUserAdvisesApi } from '@/apis/inform/user/advises.api';
import { InformAdviseModel } from '@/models/inform/advises';
import { VObject, VStore } from '@/lib/vails';
const ComHomeNoticeCardList = defineComponent({
  name: 'ComHomeNoticeCardList',
  components: {
    ComHomeNoticeCard,
    ComMoreButton,
  },
  props: {
    store: { type: Object, required: true },
    newsStore: { type: Object, required: true },
    isTeacher: { type: Boolean, required: true },
  },
  setup(props) {
    const informStore = new VStore(
      new InformUserAdvisesApi({
        params: {
          page: 1,
          per_page: 10,
          q: {
            state_eq: '已发布',
            s: ['publish_time desc'],
            inform_mod_id_eq: 4,
          },
        },
      }),
      InformAdviseModel,
    );
    const config = computed(() => ({
      store: props.store,
      pagination: { hide: true, perPage: 10 },
      mode: 'list',
      params: {},
      list: {
        gap: 16,
      },
    }));
    const teacherConfig = computed(() => ({
      store: informStore,
      pagination: { hide: true, perPage: 10 },
      mode: 'list',
      params: {},
      list: {
        gap: 16,
      },
    }));
    const newsConfig = computed(() => ({
      store: props.newsStore,
      pagination: { hide: true, perPage: 10 },
      mode: 'list',
      params: {},
      list: {
        gap: 16,
      },
    }));
    const tabs = computed(() => [
      ...(props.isTeacher
        ? [
          // {
          //   key: 'college',
          //   label: '学院公告',
          //   flag: 4,
          //   query: { inform_mod_id_eq: 4 },
          // },
          {
            key: 'department',
            label: '部门公告',
            flag: 5,
            query: { inform_mod_id_eq: 5 },
          },
        ]
        : [
          {
            key: 'college',
            label: '通知公告',
            flag: 12,
            query: { inform_mod_id_eq: 12 },
          },
        ]),
    ]);
    const toLink = (id: number) => {
      window.open(`http://os.stiei.edu.cn/portal/advises/${id}`);
    };
    const toNews = (id: number) => {
      window.open(`http://os.stiei.edu.cn/portal/news/${id}`);
    };
    const activeTabFlag = ref(tabs.value[0].flag);
    const onTabChange = (key: any) => {
      activeTabFlag.value = key.flag;
    };
    return {
      ...toRefs(props),
      tabs,
      config,
      newsConfig,
      toLink,
      toNews,
      onTabChange,
      activeTabFlag,
      teacherConfig,
    };
  },
});
export default ComHomeNoticeCardList;
</script>

<template lang="pug">
.com-home-notice-card-list.w-full.px-4
  .content.mb-4.flex
    .news.space-y-4(v-if='!isTeacher')
      .head.w-full.h-53px.text-sm.flex.items-center.pt-1 校内新闻
      .list
        TaIndexView.ta-index-view(:config='newsConfig')
          template(#card='{record}')
            ComHomeNoticeCard(:mode='true',:record='record',@click='toNews(record.id)')
      .more.flex.justify-end
        ComMoreButton.mb-4(:url='`https://www.stiei.edu.cn/91/list.htm`')
    .news.space-y-4(v-else)
      .head.w-full.h-53px.text-sm.flex.items-center.pt-1 学院公告
      .list
        TaIndexView.ta-index-view(:config='teacherConfig')
          template(#card='{record}')
            ComHomeNoticeCard(:record='record',@click='toLink(record.id)')
      .more.flex.justify-end
        ComMoreButton.mb-4(:url='`http://os.stiei.edu.cn/portal/advises?tabKey=4`')
    .notice
      .list.mb-4
        TaIndexView.ta-index-view(:config='config',:tabs='tabs',@tabChange='onTabChange')
          template(#card='{record}')
            ComHomeNoticeCard(:record='record',@click='toLink(record.id)')
      .more.flex.justify-end.mb-4
        ComMoreButton(:url='`http://os.stiei.edu.cn/portal/advises?tabKey=${activeTabFlag}`')

</template>

<style lang="stylus">
.com-home-notice-card-list
  width 100%
  background: #FFFFFF;
  border-radius: 8px;
  @media (max-width:768px) {
    padding 0
  }
  .content
    @media (max-width:1180px) {
      display flex
      flex-direction column
    }
    .news
      @media (min-width:1180px) {
        margin-right 16px
        width 50%
      }
      .head
        color #1F2A37
        border-bottom 1px solid #E5E7EB
      .list
        .ta-index-view
          .ta-index-view-header
            height 0 !important
            .header
              .table-header
                height 0 !important
          .tags-flex
            // margin-top 16px !important
            padding-left 16px !important
            padding-right 16px !important
          .list-view__pagination_placeholder
            height 0 !important
    .notice
      @media (min-width:1180px) {
        width 50%
      }
      .list
        .ta-index-view
          .ta-index-view-header
            height 0 !important
            margin-bottom 4px !important
            .header
              .table-header
                height 0 !important
          .ta-index-view-ta-tab
            border-bottom 1px solid #E5E7EB
            .ta-tab
              padding-left 16px
              .tab-label
                font-size 14px
                line-height 150%
                font-weight 400
                color #1F2A37 !important
              .cursor
                height 2px !important
                bottom 0 !important
          .tags-flex
            margin-top 16px !important
            padding-left 16px !important
            padding-right 16px !important
          .list-view__pagination_placeholder
            height 0 !important
</style>
