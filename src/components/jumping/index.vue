<script lang="ts">
import { ref, defineComponent, toRefs, watch } from 'vue';
import { useRoute } from 'vue-router';

const jumping = defineComponent({
  name: 'jumping',
  components: {},
  props: {
    value: { type: String, default: 'copy' },
  },
  setup(props) {
    let msg = ref('');
    const route = useRoute();
    const website = ref('');

    watch(
      () => route.query.website,
      () => {
        website.value = route.query.website as string;
      },
      { immediate: true },
    );

    return {
      ...toRefs(props),
      msg,
      website,
    };
  },
});
export default jumping;
</script>

<template lang="pug">
.jumping
  iframe.iframe(:src="website" frameborder="0")
</template>

<style lang="stylus" scoped>
.jumping
  width 100%
  height 100%
  .iframe
    width 100%
    height 100%
</style>
