<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';
const ComDemandPlanCard = defineComponent({
  name: 'ComDemandPlanCard',
  components: {},
  props: {
    value: { type: String, default: 'copy' },
    record: { type: Object, required: true },
    editRecord: { type: Object, required: true },
    plus: { type: Boolean, default: false },
    index: { type: String, default: '' },
  },
  setup(props, { emit }) {
    const onDelete = () => emit('delete', props.record);

    return {
      ...toRefs(props),
      onDelete,
    };
  },
});
export default ComDemandPlanCard;
</script>

<template lang="pug">
.com-demand-plan-card.flex.pt-2
  div.orderNum(class="mr-[10px]")
    div.circle.rounded-full.flex.items-center.justify-center
      div {{index+1}}.
  div.w-full
    div.flex.justify-between
      div
        div.subtitle.mb-1 需求单位
        div.content.mb-2 {{record.user.name}}
      div.delete.cursor-pointer(@click="onDelete",v-if="plus") 删除
    div
      div.subtitle.mb-1 对接计划
      div.content.mb-2 {{ record.payload?.对接计划 }}

</template>

<style lang="stylus" scoped>
.circle
  background: #EFF6FF;
  width 24px
  height 24px
  font-size: 14px;
  font-family: ComicSansMS-Bold, ComicSansMS;
  font-weight: bold;
  color: #262626;
  line-height: 19px;
.subtitle
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #A1A1AA;
  line-height: 18px;
.content
  font-size: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #262626;
  line-height: 20px;
.delete
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #3B82F6;
  line-height: 18px;
</style>
