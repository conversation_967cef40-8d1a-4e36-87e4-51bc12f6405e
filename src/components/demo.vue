<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';
const demo = defineComponent({
  name: 'demo',
  components: {},
  props: {
    value: { type: String, default: 'copy' },
  },
  setup(props) {
    const count = ref(0);
    setInterval(() => {
      if (count.value + 1 < 10) {
        count.value = count.value + 1;
      } else {
        count.value = 0;
      }
    }, 1200);

    return {
      ...toRefs(props),
      count,
    };
  },
});
export default demo;
</script>

<template lang="pug">
.demo
  .container
    ul.flip(:title='count+1+1<9?count+1:1')
      li
        a(href='#')
          .up
            .inn {{count}}
          .down
            .inn {{count}}
</template>

<style lang="stylus" scoped>
ul
  list-style none
ul,li
  display inline-block
.demo
  .container
    .flip
      position relative
      height 80px
      width 49px
      border-radius 4px
      z-index 2
      box-shadow 0 2px 5px rgba(0, 0, 0, .7)
      &::after
        content attr(title)
        font-size 64px
        font-weight bold
        color #fff
        width 100%
        text-align center
        line-height 80px
        background #000
        position absolute
        left 0;bottom 0;right 0;top 0;

        z-index 1
      li
        position absolute
        left 0;bottom 0;right 0;top 0;
        z-index 2
        a
          display block
          height 100%
          perspective 200px
          transform-style: preserve-3d;
          .up
            left 0;top 0;
            position absolute;
            height 50%
            width 100%
            transform-origin 50% 100%
            animation turn2 1.2s linear infinite both
            z-index 1

            .inn
              font-size 64px
              font-weight bold
              color #fff
              text-align center
              position absolute
              line-height 80px
              left 0;top 0;
              width 100%
              height 200%
              overflow hidden
              background #000

          .down
            left 0;bottom 0;
            position absolute
            height 50%
            width 100%
            transform-origin 50% 0%
            // animation turn 1.2s linear 1.2s  both
            z-index 1
            .inn
              font-size 64px
              font-weight bold
              color #fff
              text-align center
              line-height 80px
              position absolute
              left 0;bottom 0;
              width 100%
              height 200%
              overflow hidden
              text-shadow 0 1px 2px #000
              background #000
@keyframes turn {
    0% {
        transform rotateX(90deg);
        opactity:1;

    }
    100% {
        transform: rotateX(0deg);
        opactity:0;

    }
}
@keyframes turn2 {
    0% {
        transform: rotateX(0deg);
        opactity:1;
    }
    100%{
      transform: rotateX(-180deg);
      opactity:0;
    }
}
</style>
