<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';
const ComAppsCardTabs = defineComponent({
  name: 'ComAppsCardTabs',
  components: {},
  props: {
    tabs: { type: Array, required: true },
  },
  setup(props, { emit }) {
    const currentTab = ref(props.tabs[0]);
    const changeTab = (tab: any) => {
      emit('change', tab);
      currentTab.value = tab;
    };
    return {
      ...toRefs(props),
      changeTab,
      currentTab,
    };
  },
});
export default ComAppsCardTabs;
</script>

<template lang="pug">
.com-apps-card-tabs.flex.gap-4.items-center
  .h-full.flex.items-center.flex-shrink-0(v-for='tab in tabs',@click='changeTab(tab)',:class='`${currentTab.label === tab.label?"activeTab":""}`')
    .tab.text-sm.leading-normal {{ tab.label }}
</template>

<style lang="stylus">
.com-apps-card-tabs
  .tab
    color #1f2a37
  .activeTab
    border-bottom 1px solid #528ff5
</style>
